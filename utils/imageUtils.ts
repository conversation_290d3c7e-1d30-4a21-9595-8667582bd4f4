export const convertGCSPathToURL = (gcsPath: string): string => {
  if (!gcsPath || typeof gcsPath !== 'string') {
    return '';
  }

  // Check if it's already a URL or data URL
  if (gcsPath.startsWith('http') || gcsPath.startsWith('data:')) {
    return gcsPath;
  }
  
  // Convert Google Cloud Storage paths to HTTPS URLs
  if (gcsPath.startsWith('gs://')) {
    // Extract bucket name and object path from the gs:// URL
    const gsPattern = /gs:\/\/([^/]+)\/(.+)/;
    const match = gcsPath.match(gsPattern);
    
    if (match && match.length === 3) {
      const [, bucketName, objectPath] = match;
      // Format: https://storage.googleapis.com/BUCKET_NAME/OBJECT_PATH
      return `https://storage.googleapis.com/${bucketName}/${objectPath}`;
    }
  }
  
  return gcsPath;
};

export const processImageSource = (imageSource: string): string => {
  if (!imageSource || typeof imageSource !== 'string') {
    return '';
  }

  console.log('Processing image source:', imageSource.substring(0, 50) + '...');

  // If it's already a data URL, return it as is
  if (imageSource.startsWith('data:image/')) {
    console.log('Detected as data URL');
    return imageSource;
  }

  // Handle Google Cloud Storage paths
  if (imageSource.startsWith('gs://')) {
    console.log('Detected as Google Cloud Storage path');
    const url = convertGCSPathToURL(imageSource);
    console.log('Converted to:', url);
    return url;
  }

  // Handle base64 encoded PNG images
  if (imageSource.startsWith('iVBOR')) {
    console.log('Detected as base64 PNG');
    return `data:image/png;base64,${imageSource}`;
  }

  // Handle other base64 encoded images (likely JPEG)
  if (imageSource.match(/^[A-Za-z0-9+/=]+$/) && imageSource.length > 20) {
    if (imageSource.startsWith('/9j/')) {
      console.log('Detected as base64 JPEG');
      return `data:image/jpeg;base64,${imageSource}`;
    } else if (imageSource.length > 100) {
      // Long base64 string - likely an image
      console.log('Detected as generic base64');
      // Default to JPEG if we can't determine the type
      return `data:image/jpeg;base64,${imageSource}`;
    }
  }

  // If it's a URL, return it as is
  if (imageSource.startsWith('http')) {
    console.log('Detected as HTTP URL');
    return imageSource;
  }

  // For anything else, try the conversion again
  console.log('Unknown format, trying GCS conversion');
  return convertGCSPathToURL(imageSource);
};
