'use client';

import React, { Suspense } from 'react';
import { ChatProvider } from './_logics/chat_context';
import { ChatContainer } from './widgets/ChatContainer';
import { ChatDebugPanel } from './widgets/ChatDebugPanel';

const ChatLoading = () => (
  <div className="flex items-center justify-center h-screen">
    <div className="text-center">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#006060] mx-auto"></div>
      <p className="mt-4 text-gray-600">Loading chat...</p>
    </div>
  </div>
);

export default function ChatPage() {
  return (
    <div className="w-full h-full flex flex-col" style={{ height: 'calc(100vh - 64px)', maxHeight: 'calc(100vh - 64px)' }}>
      <Suspense fallback={<ChatLoading />}>
        <ChatProvider>
          <ChatContainer />
          <ChatDebugPanel />
        </ChatProvider>
      </Suspense>
    </div>
  );
}
