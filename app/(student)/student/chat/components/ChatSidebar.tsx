'use client';

import React, { useState } from 'react';
import { useChatContext } from '../_logics/chat_context';
import { format } from 'date-fns';
import { useTeacherTheme } from '@/components/general/TeacherTheme/TeachersTheme';

export const ChatSidebar = () => {
  const { state, setActiveRoom, searchChats } = useChatContext();
  const [searchTerm, setSearchTerm] = useState('');
  const { isDark } = useTeacherTheme();
  const now = new Date();
  
  // Get theme colors
  const themeColors = {
    bgSidebar: isDark ? '#252B42' : '#ffffff',
    bgSidebarHover: isDark ? '#1e232e' : '#f5f5f5',
    textPrimary: isDark ? '#ffffff' : '#333333',
    textSecondary: isDark ? '#9ca3af' : '#666666',
    border: isDark ? '#384058' : '#e0e0e0',
    accent: '#006060'
  };

  const [activeTab, setActiveTab] = useState<'all' | 'unread' | 'Pages'>('all');
  const [searchQuery, setSearchQuery] = useState('');

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);
    searchChats(query);
  };

  const handleTabClick = (tab: 'all' | 'unread' | 'Pages') => {
    setActiveTab(tab);
  };

  const formatTime = (date: Date) => {
    try {
      const messageDate = new Date(date);
      
      // If it's today, just show the time
      if (messageDate.toDateString() === new Date().toDateString()) {
        return format(messageDate, 'h:mm a');
      }
      
      // If it's within the last week, show the day name
      const daysDiff = Math.floor((new Date().getTime() - messageDate.getTime()) / (1000 * 60 * 60 * 24));
      if (daysDiff < 7) {
        return format(messageDate, 'EEE');
      }
      
      // Otherwise show the date
      return format(messageDate, 'MMM d');
    } catch (error) {
      return '';
    }
  };

  return (
    <div className={`w-[280px] flex-shrink-0 border-r ${isDark ? 'border-[#384058] bg-[#252B42]' : 'border-gray-200 bg-white'} h-full flex flex-col`}>
      <div className={`px-3 py-3 border-b ${isDark ? 'border-[#384058]' : 'border-gray-200'}`}>
        <div className="flex items-center">
          <img src="/logo.png" alt="LearnKonnect" className="w-6 h-6 mr-2" />
          <div className="flex flex-col">
            <div className="flex items-center">
              <h2 className={`font-medium ${isDark ? 'text-white' : 'text-[#006060]'} text-base`}>LearnKonnect</h2>
              <span className={`font-medium ${isDark ? 'text-white' : 'text-[#006060]'} text-base ml-1`}>community</span>
            </div>
            <p className={`text-xs ${isDark ? 'text-gray-200' : 'text-gray-500'}`}>Communities pages</p>
          </div>
        </div>
      </div>
      
      <div className="px-3 py-2 relative">
        <div className="relative flex items-center justify-between">
          <div className="relative text-white w-2/3">
            <input 
              type="text" 
              placeholder="Search..." 
              className={`w-full py-1.5 pl-7 pr-2 ${isDark ? 'bg-[#1e232e] border-[#384058] text-white placeholder-gray-300' : 'bg-gray-100 border-gray-200 text-gray-800 placeholder-gray-500'} border rounded-lg focus:outline-none focus:ring-1 focus:ring-[#006060] text-xs`}
              value={searchTerm}
              onChange={handleSearch}
            />
            <svg 
              className={`absolute left-2 top-1/2 h-3.5 w-3.5 -translate-y-1/2 ${isDark ? 'text-gray-300' : 'text-gray-500'}`}
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24" 
              xmlns="http://www.w3.org/2000/svg"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          
          <button className="bg-[#006060] hover:bg-[#004645] text-white rounded-md p-1.5 flex items-center justify-center transition-colors duration-200">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
          </button>
        </div>
      </div>
      
      <div className={`flex border-b ${isDark ? 'border-[#384058]' : 'border-gray-200'} mb-2`}>
        <button 
          className={`flex-1 py-2 text-sm font-medium ${activeTab === 'all' ? (isDark ? 'text-white border-b-2 border-white' : 'text-[#006060] border-b-2 border-[#006060]') : (isDark ? 'text-gray-300' : 'text-gray-500')}`}
          onClick={() => handleTabClick('all')}
        >
          All
        </button>
        <button 
          className={`flex-1 py-2 text-sm font-medium ${activeTab === 'unread' ? (isDark ? 'text-white border-b-2 border-white' : 'text-[#006060] border-b-2 border-[#006060]') : (isDark ? 'text-gray-300' : 'text-gray-500')}`}
          onClick={() => handleTabClick('unread')}
        >
          Unread
        </button>
        <button 
          className={`flex-1 py-2 text-sm font-medium ${activeTab === 'Pages' ? (isDark ? 'text-white border-b-2 border-white' : 'text-[#006060] border-b-2 border-[#006060]') : (isDark ? 'text-gray-300' : 'text-gray-500')}`}
          onClick={() => handleTabClick('Pages')}
        >
          Pages
        </button>
      </div>
      
      <div className="overflow-y-auto flex-1">
        <div className="px-3 py-2 relative">
          <h3 className={`text-xs font-medium ${isDark ? 'text-gray-300' : 'text-gray-500'} uppercase`}>Pinned Chats</h3>
        </div>
        
        {state.rooms.slice(0, 8).map((room) => (
          <div 
            key={room.id}
            className={`flex items-center p-3 cursor-pointer transition-colors duration-200 ${isDark ? 
              (state.activeRoomId === room.id ? 'bg-[#1e232e]' : 'hover:bg-[#1e232e]') : 
              (state.activeRoomId === room.id ? 'bg-gray-100' : 'hover:bg-gray-50')
            } rounded-lg mx-1 my-0.5`}
            onClick={() => setActiveRoom(room.id)}
          >
            <div className="relative">
              <img 
                src={room.avatar} 
                alt={room.name} 
                className={`w-10 h-10 rounded-full object-cover shadow-md border ${isDark ? 'border-gray-700' : 'border-gray-200'}`}
                // Use the project logo as placeholder
                onError={(e) => {
                  e.currentTarget.src = '/logo.png';
                }}
              />
              {room.isGroup && room.id === "2" && (
                <span className={`absolute bottom-0 right-0 w-3 h-3 bg-[#006060] border-2 ${isDark ? 'border-gray-800' : 'border-white'} rounded-full`}></span>
              )}
            </div>
            <div className="ml-3 flex-1 overflow-hidden">
              <div className="flex justify-between items-center">
                <p className={`text-sm font-medium ${isDark ? 'text-white' : 'text-gray-800'} truncate`}>{room.name}</p>
                <p className={`text-xs ${isDark ? 'text-gray-300' : 'text-gray-500'} whitespace-nowrap ml-1`}>{formatTime(room.timestamp)}</p>
              </div>
              <div className="flex items-center mt-0.5">
                <p className={`text-xs ${isDark ? 'text-gray-300' : 'text-gray-500'} truncate`}>{room.lastMessage}</p>
                {room.id === "2" && (
                  <span className="ml-2 inline-flex items-center justify-center w-5 h-5 text-xs font-bold text-white bg-[#006060] rounded-full shadow-sm">2</span>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
