'use client';

import React, { useState } from 'react';
import { useChat } from '../_logics/chat_context';
import { format } from 'date-fns';
import { useTeacherTheme } from '@/components/general/TeacherTheme/TeachersTheme';
import { CreateChatGroupModal } from './CreateChatGroupModal';
import { useApi } from '@/hooks/useRequest';

export const ChatSidebar = () => {
  const { state, setActiveRoom, searchChats, refreshChatRooms, joinSpecificChatGroup } = useChat();
  const { request } = useApi();
  const [searchTerm, setSearchTerm] = useState('');
  const { isDark } = useTeacherTheme();
  const [showCreateGroupModal, setShowCreateGroupModal] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [showAvailableGroups, setShowAvailableGroups] = useState(false);
  const [availableGroups, setAvailableGroups] = useState<any[]>([]);
  const [loadingGroups, setLoadingGroups] = useState(false);
  const [isJoining, setIsJoining] = useState(false);

  const [activeTab, setActiveTab] = useState<'all' | 'direct' | 'group'>('all');

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchTerm(query);
    searchChats(query);
  };

  const handleTabClick = (tab: 'all' | 'direct' | 'group') => {
    setActiveTab(tab);

    // We'll use the room filtering in the render section based on activeTab
    // Clear any search term when switching tabs to show all relevant rooms
    setSearchTerm('');
    searchChats('');
  };

  const formatTime = (date: Date | string) => {
    if (!date) return '';

    try {
      // Convert to Date object if it's not already one
      const messageDate = date instanceof Date ? date : new Date(date);

      // Check if the date is valid
      if (isNaN(messageDate.getTime())) {
        return '';
      }

      // If it's today, just show the time
      if (messageDate.toDateString() === new Date().toDateString()) {
        return format(messageDate, 'h:mm a');
      }

      // If it's within the last week, show the day name
      const daysDiff = Math.floor((new Date().getTime() - messageDate.getTime()) / (1000 * 60 * 60 * 24));
      if (daysDiff < 7) {
        return format(messageDate, 'EEE');
      }

      // Otherwise show the date
      return format(messageDate, 'MMM d');
    } catch (error) {
      console.error('Error formatting date:', error);
      return '';
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await refreshChatRooms();
    } catch (error) {
      console.error('Failed to refresh chat rooms:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  // Fetch available groups to join
  const fetchAvailableGroups = async () => {
    setLoadingGroups(true);
    try {
      console.log('Fetching available groups...');

      // Try different endpoints to find available groups
      const endpoints = [
        '/chat-group', // Current user's groups
        '/chat-group/public', // Public groups (if exists)
        '/chat-group/all', // All groups (if exists)
      ];

      let allGroups: any[] = [];

      for (const endpoint of endpoints) {
        try {
          console.log(`Trying endpoint: ${endpoint}`);
          const response = await request("GET", endpoint, null, "application/json");
          console.log(`Response from ${endpoint}:`, response);

          if (response && response.status === 200) {
            let groups = [];

            // Handle different response structures
            if (response.data?.data?.chat_groups) {
              groups = response.data.data.chat_groups;
            } else if (response.data?.data && Array.isArray(response.data.data)) {
              groups = response.data.data;
            } else if (response.data?.chat_groups) {
              groups = response.data.chat_groups;
            } else if (Array.isArray(response.data)) {
              groups = response.data;
            }

            console.log(`Found ${groups.length} groups from ${endpoint}`);
            allGroups = [...allGroups, ...groups];
          }
        } catch (endpointError) {
          console.log(`Endpoint ${endpoint} failed:`, endpointError);
        }
      }

      // Remove duplicates based on ID
      const uniqueGroups = allGroups.filter((group, index, self) =>
        index === self.findIndex(g => g.id === group.id)
      );

      console.log('All available groups:', uniqueGroups);
      setAvailableGroups(uniqueGroups);

    } catch (error) {
      console.error('Error fetching available groups:', error);
      setAvailableGroups([]);
    } finally {
      setLoadingGroups(false);
    }
  };

  const handleJoinGroup = async (groupId: string, groupName: string) => {
    setIsJoining(true);
    try {
      console.log(`Attempting to join group: ${groupId} (${groupName})`);
      const success = await joinSpecificChatGroup(groupId);
      if (success) {
        setShowAvailableGroups(false);
        // Refresh to show the newly joined group
        await refreshChatRooms();
        alert(`Successfully joined "${groupName}"!`);
      } else {
        alert(`Failed to join "${groupName}". You may already be a member or lack permission.`);
      }
    } catch (error) {
      console.error('Error joining group:', error);
      alert(`Failed to join "${groupName}". Please try again.`);
    } finally {
      setIsJoining(false);
    }
  };

  return (
    <div className={`w-[280px] flex-shrink-0 border-r ${isDark ? 'border-[#384058] bg-[#252B42]' : 'border-gray-200 bg-white'} h-full flex flex-col`}>
      <div className={`px-3 py-3 border-b ${isDark ? 'border-[#384058]' : 'border-gray-200'}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <img src="/logo.png" alt="LearnKonnect" className="w-6 h-6 mr-2" />
            <div className="flex flex-col">
              <div className="flex items-center">
                <h2 className={`font-medium ${isDark ? 'text-white' : 'text-[#006060]'} text-base`}>LearnKonnect</h2>
                <span className={`font-medium ${isDark ? 'text-white' : 'text-[#006060]'} text-base ml-1`}>community</span>
              </div>
              <p className={`text-xs ${isDark ? 'text-gray-200' : 'text-gray-500'}`}>Communities pages</p>
            </div>
          </div>
          <button
            onClick={handleRefresh}
            disabled={isRefreshing}
            className={`p-1.5 ${isDark ? 'text-white hover:bg-[#1e232e]' : 'text-gray-700 hover:bg-gray-100'} rounded-full transition-colors ${isRefreshing ? 'opacity-50 cursor-not-allowed' : ''}`}
            title="Refresh Chat Rooms"
          >
            <svg
              className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              ></path>
            </svg>
          </button>
        </div>
      </div>

      <div className="px-3 py-2 relative">
        <div className="relative flex items-center justify-between">
          <div className="relative text-white w-2/3">
            <input
              type="text"
              placeholder="Search..."
              className={`w-full py-1.5 pl-7 pr-2 ${isDark ? 'bg-[#1e232e] border-[#384058] text-white placeholder-gray-300' : 'bg-gray-100 border-gray-200 text-gray-800 placeholder-gray-500'} border rounded-lg focus:outline-none focus:ring-1 focus:ring-[#006060] text-xs`}
              value={searchTerm}
              onChange={handleSearch}
            />
            <svg
              className="w-4 h-4 absolute left-2 top-2 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              ></path>
            </svg>
          </div>
          <div className="flex space-x-1">
            <button
              className={`p-1.5 ${isDark ? 'text-white hover:bg-[#1e232e]' : 'text-gray-700 hover:bg-gray-100'} rounded-full`}
              onClick={() => {
                setShowAvailableGroups(!showAvailableGroups);
                if (!showAvailableGroups) {
                  fetchAvailableGroups();
                }
              }}
              title="Browse Available Groups"
            >
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"
                ></path>
              </svg>
            </button>
            <button
              className={`p-1.5 ${isDark ? 'text-white hover:bg-[#1e232e]' : 'text-gray-700 hover:bg-gray-100'} rounded-full`}
              onClick={() => setShowCreateGroupModal(true)}
              title="Create New Group"
            >
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                ></path>
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Available Groups to Join */}
      {showAvailableGroups && (
        <div className={`px-3 py-2 border-b ${isDark ? 'border-[#384058]' : 'border-gray-200'} max-h-48 overflow-y-auto`}>
          <div className="flex justify-between items-center mb-2">
            <h4 className={`text-xs font-medium ${isDark ? 'text-gray-300' : 'text-gray-600'} uppercase`}>
              Available Groups
            </h4>
            <button
              onClick={() => setShowAvailableGroups(false)}
              className={`text-xs ${isDark ? 'text-gray-400 hover:text-white' : 'text-gray-500 hover:text-gray-700'}`}
            >
              ✕
            </button>
          </div>

          {loadingGroups ? (
            <div className="flex items-center justify-center py-4">
              <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-[#006060]"></div>
              <span className={`ml-2 text-xs ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>Loading...</span>
            </div>
          ) : availableGroups.length === 0 ? (
            <div className="text-center py-4">
              <p className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                No available groups found
              </p>
              <button
                onClick={fetchAvailableGroups}
                className={`mt-2 px-2 py-1 text-xs ${isDark ? 'bg-[#384058] hover:bg-[#4a5568] text-white' : 'bg-gray-100 hover:bg-gray-200 text-gray-800'} rounded`}
              >
                Retry
              </button>
            </div>
          ) : (
            <div className="space-y-1">
              {availableGroups.map((group) => (
                <div
                  key={group.id}
                  className={`flex items-center justify-between p-2 ${isDark ? 'bg-[#1e232e] hover:bg-[#2a3441]' : 'bg-gray-50 hover:bg-gray-100'} rounded text-xs`}
                >
                  <div className="flex-1 min-w-0">
                    <p className={`font-medium truncate ${isDark ? 'text-white' : 'text-gray-800'}`}>
                      {group.name || 'Unnamed Group'}
                    </p>
                    <p className={`text-xs truncate ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                      {group.description || 'No description'}
                    </p>
                    <p className={`text-xs ${isDark ? 'text-gray-500' : 'text-gray-400'}`}>
                      ID: {group.id}
                    </p>
                  </div>
                  <button
                    onClick={() => handleJoinGroup(group.id, group.name || 'Unnamed Group')}
                    disabled={isJoining}
                    className={`ml-2 px-2 py-1 text-xs bg-[#006060] text-white rounded hover:bg-[#004e4e] disabled:opacity-50 disabled:cursor-not-allowed flex-shrink-0`}
                  >
                    {isJoining ? 'Joining...' : 'Join'}
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      <div className={`flex border-b ${isDark ? 'border-[#384058]' : 'border-gray-200'} mb-2`}>
        <button
          className={`flex-1 py-2 text-sm font-medium ${activeTab === 'all' ? (isDark ? 'text-white border-b-2 border-white' : 'text-[#006060] border-b-2 border-[#006060]') : (isDark ? 'text-gray-300' : 'text-gray-500')}`}
          onClick={() => handleTabClick('all')}
        >
          All
        </button>
        <button
          className={`flex-1 py-2 text-sm font-medium ${activeTab === 'direct' ? (isDark ? 'text-white border-b-2 border-white' : 'text-[#006060] border-b-2 border-[#006060]') : (isDark ? 'text-gray-300' : 'text-gray-500')}`}
          onClick={() => handleTabClick('direct')}
        >
          Direct
        </button>
        <button
          className={`flex-1 py-2 text-sm font-medium ${activeTab === 'group' ? (isDark ? 'text-white border-b-2 border-white' : 'text-[#006060] border-b-2 border-[#006060]') : (isDark ? 'text-gray-300' : 'text-gray-500')}`}
          onClick={() => handleTabClick('group')}
        >
          Group
        </button>
      </div>

      <div className="overflow-y-auto flex-1">
        {state.error && (
          <div className="px-3 py-2 mb-2">
            <div className="bg-red-100 border border-red-400 text-red-700 px-3 py-2 rounded text-sm">
              {state.error}
            </div>
          </div>
        )}

        {state.isLoading ? (
          <div className="flex items-center justify-center h-24">
            <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-[#006060]"></div>
          </div>
        ) : state.rooms.length === 0 ? (
          <div className="px-4 py-8 text-center">
            <p className={`text-sm ${isDark ? 'text-gray-300' : 'text-gray-500'}`}>
              No chat rooms available.
            </p>
            <div className="mt-3 space-y-2">
              <button
                onClick={handleRefresh}
                className={`block w-full px-3 py-1 text-xs ${isDark ? 'bg-[#384058] hover:bg-[#4a5568] text-white' : 'bg-gray-100 hover:bg-gray-200 text-gray-800'} rounded transition-colors`}
              >
                Refresh
              </button>
              <button
                onClick={() => {
                  setShowAvailableGroups(true);
                }}
                className={`block w-full px-3 py-1 text-xs bg-[#006060] hover:bg-[#004e4e] text-white rounded transition-colors`}
              >
                Join Test Group
              </button>
            </div>
          </div>
        ) : (
          <>
            {activeTab === 'all' && (
              <div className="px-3 py-2 relative">
                <h3 className={`text-xs font-medium ${isDark ? 'text-gray-300' : 'text-gray-500'} uppercase`}>All Chats</h3>
              </div>
            )}
            {activeTab === 'direct' && (
              <div className="px-3 py-2 relative">
                <h3 className={`text-xs font-medium ${isDark ? 'text-gray-300' : 'text-gray-500'} uppercase`}>Direct Messages</h3>
              </div>
            )}
            {activeTab === 'group' && (
              <div className="px-3 py-2 relative">
                <h3 className={`text-xs font-medium ${isDark ? 'text-gray-300' : 'text-gray-500'} uppercase`}>Group Chats</h3>
              </div>
            )}

            {state.rooms
              .filter(room => {
                if (activeTab === 'all') return true;
                if (activeTab === 'direct') return !room.isGroup;
                if (activeTab === 'group') return room.isGroup;
                return false;
              })
              .map((room) => (
              <div
                key={room.id}
                className={`flex items-center p-3 cursor-pointer transition-colors duration-200 ${isDark ?
                  (state.activeRoomId === room.id ? 'bg-[#1e232e]' : 'hover:bg-[#1e232e]') :
                  (state.activeRoomId === room.id ? 'bg-gray-100' : 'hover:bg-gray-50')
                } rounded-lg mx-1 my-0.5`}
                onClick={() => setActiveRoom(room.id)}
              >
                <div className="relative">
                  {/* Always use logo for chat avatars instead of course images */}
                  <img
                    src="/logo.png"
                    alt={room.name}
                    className={`w-10 h-10 rounded-full object-cover shadow-md border ${isDark ? 'border-gray-700' : 'border-gray-200'}`}
                  />
                </div>
                <div className="ml-3 flex-1 overflow-hidden">
                  <div className="flex justify-between items-center">
                    <p className={`text-sm font-medium ${isDark ? 'text-white' : 'text-gray-800'} truncate`}>{room.name}</p>
                    <p className={`text-xs ${isDark ? 'text-gray-300' : 'text-gray-500'} whitespace-nowrap ml-1`}>{formatTime(room.timestamp)}</p>
                  </div>
                  <div className="flex items-center justify-between mt-0.5">
                    <p className={`text-xs ${isDark ? 'text-gray-300' : 'text-gray-500'} truncate flex-1`}>
                      {room.lastMessage || 'No messages yet'}
                    </p>
                    {room.isGroup && room.participants && room.participants.length > 0 && (
                      <span className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-400'} ml-2 flex-shrink-0`}>
                        {room.participants.length} members
                      </span>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </>
        )}
      </div>

      {/* Create Chat Group Modal */}
      <CreateChatGroupModal
        isOpen={showCreateGroupModal}
        onClose={() => setShowCreateGroupModal(false)}
        onSuccess={() => {
          // Refetch chat rooms after creating a new group
          searchChats('');
        }}
      />
    </div>
  );
};
