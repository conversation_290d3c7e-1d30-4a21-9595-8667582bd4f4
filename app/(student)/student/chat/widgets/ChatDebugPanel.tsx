'use client';

import React, { useState } from 'react';
import { useChat } from '../_logics/chat_context';
import { ChatApiService } from '../_logics/chat_api_service';
import { getCurrentUser } from '../_logics/chat_api_service';
import { useTeacherTheme } from '@/components/general/TeacherTheme/TeachersTheme';

export const ChatDebugPanel: React.FC = () => {
  const { state, refreshChatRooms } = useChat();
  const { isDark } = useTeacherTheme();
  const [isOpen, setIsOpen] = useState(false);
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  const chatApiService = ChatApiService.getInstance();

  const runDiagnostics = async () => {
    setIsLoading(true);
    const diagnostics: any = {
      timestamp: new Date().toISOString(),
      user: null,
      apiTest: null,
      chatGroups: null,
      errors: []
    };

    try {
      // Test 1: Check current user
      console.log('=== CHAT DEBUG: Testing current user ===');
      const user = getCurrentUser();
      diagnostics.user = user;
      console.log('Current user:', user);

      if (!user) {
        diagnostics.errors.push('No authenticated user found');
        setDebugInfo(diagnostics);
        setIsLoading(false);
        return;
      }

      // Test 2: Test API connection
      console.log('=== CHAT DEBUG: Testing API connection ===');
      try {
        const apiResponse = await chatApiService.getAllChatGroups();
        diagnostics.apiTest = {
          success: true,
          response: apiResponse,
          count: apiResponse.length
        };
        console.log('API test successful:', apiResponse);
      } catch (apiError) {
        diagnostics.apiTest = {
          success: false,
          error: apiError
        };
        diagnostics.errors.push(`API test failed: ${apiError}`);
        console.error('API test failed:', apiError);
      }

      // Test 3: Check current state
      console.log('=== CHAT DEBUG: Current chat state ===');
      diagnostics.chatGroups = {
        stateRooms: state.rooms,
        stateRoomsCount: state.rooms.length,
        activeRoomId: state.activeRoomId,
        isLoading: state.isLoading,
        error: state.error
      };
      console.log('Current chat state:', diagnostics.chatGroups);

    } catch (error) {
      diagnostics.errors.push(`Diagnostics failed: ${error}`);
      console.error('Diagnostics failed:', error);
    }

    setDebugInfo(diagnostics);
    setIsLoading(false);
  };

  const createTestGroup = async () => {
    try {
      setIsLoading(true);
      const testGroupData = {
        name: `Test Group ${Date.now()}`,
        description: "A test group created for debugging",
        type: "student",
        course_id: "",
        is_private: false,
        initial_members: []
      };

      console.log('Creating test group:', testGroupData);
      const result = await chatApiService.createChatGroup(testGroupData);
      console.log('Test group created:', result);
      
      // Refresh the chat rooms
      await refreshChatRooms();
      
      alert('Test group created successfully!');
    } catch (error) {
      console.error('Failed to create test group:', error);
      alert(`Failed to create test group: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) {
    return (
      <button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-4 right-4 bg-red-500 text-white px-3 py-2 rounded-lg text-sm z-50"
        title="Open Debug Panel"
      >
        🐛 Debug
      </button>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 w-96 max-h-96 overflow-y-auto bg-gray-900 text-white p-4 rounded-lg shadow-xl z-50 text-xs">
      <div className="flex justify-between items-center mb-3">
        <h3 className="font-bold text-sm">Chat Debug Panel</h3>
        <button
          onClick={() => setIsOpen(false)}
          className="text-gray-400 hover:text-white"
        >
          ✕
        </button>
      </div>

      <div className="space-y-2">
        <button
          onClick={runDiagnostics}
          disabled={isLoading}
          className="w-full bg-blue-600 hover:bg-blue-700 px-3 py-2 rounded text-sm disabled:opacity-50"
        >
          {isLoading ? 'Running...' : 'Run Diagnostics'}
        </button>

        <button
          onClick={createTestGroup}
          disabled={isLoading}
          className="w-full bg-green-600 hover:bg-green-700 px-3 py-2 rounded text-sm disabled:opacity-50"
        >
          Create Test Group
        </button>

        <button
          onClick={refreshChatRooms}
          disabled={isLoading}
          className="w-full bg-purple-600 hover:bg-purple-700 px-3 py-2 rounded text-sm disabled:opacity-50"
        >
          Refresh Chat Rooms
        </button>
      </div>

      {debugInfo && (
        <div className="mt-4 space-y-2">
          <div className="border-t border-gray-700 pt-2">
            <h4 className="font-semibold text-yellow-400">Debug Results:</h4>
            
            <div className="mt-2">
              <strong>User:</strong>
              <pre className="bg-gray-800 p-2 rounded mt-1 overflow-x-auto">
                {JSON.stringify(debugInfo.user, null, 2)}
              </pre>
            </div>

            <div className="mt-2">
              <strong>API Test:</strong>
              <pre className="bg-gray-800 p-2 rounded mt-1 overflow-x-auto">
                {JSON.stringify(debugInfo.apiTest, null, 2)}
              </pre>
            </div>

            <div className="mt-2">
              <strong>Chat State:</strong>
              <pre className="bg-gray-800 p-2 rounded mt-1 overflow-x-auto">
                {JSON.stringify(debugInfo.chatGroups, null, 2)}
              </pre>
            </div>

            {debugInfo.errors.length > 0 && (
              <div className="mt-2">
                <strong className="text-red-400">Errors:</strong>
                <ul className="list-disc list-inside text-red-300">
                  {debugInfo.errors.map((error: string, index: number) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
