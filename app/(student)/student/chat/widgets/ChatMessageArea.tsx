'use client';

import React, { useRef, useEffect, useState } from 'react';
import { useChatContext } from '../_logics/chat_context';
import { format } from 'date-fns';
import { useTeacherTheme } from '@/components/general/TeacherTheme/TeachersTheme';

export const ChatMessageArea = () => {
  const { state, searchMessages } = useChatContext();
  const { isDark } = useTeacherTheme();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const activeRoom = state.rooms.find(room => room.id === state.activeRoomId);
  const messages = state.activeRoomId ? state.messages[state.activeRoomId] || [] : [];
  
  const [isSearchActive, setIsSearchActive] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [showInfoDrawer, setShowInfoDrawer] = useState(false);
  
  // Get theme colors
  const themeColors = {
    primary: isDark ? '#6d7fae' : '#006060',
    bg: isDark ? '#252B42' : '#ffffff',
    text: isDark ? '#ffffff' : '#333333',
    border: isDark ? '#384058' : '#e0e0e0'
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const formatMessageTime = (date: Date) => {
    return format(new Date(date), 'hh:mm a');
  };

  // Handle search functionality
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);
    if (state.activeRoomId) {
      searchMessages(state.activeRoomId, query);
    }
  };
  
  // Handle menu actions
  const handleMenuAction = (action: string) => {
    // Close dropdown after action
    setDropdownOpen(false);
    
    switch(action) {
      case 'info':
        setShowInfoDrawer(true);
        break;
      case 'clear':
        // In a real app, this would call an API to clear chat history
        console.log(`Clearing chat history for: ${activeRoom?.name}`);
        break;
      case 'close':
        // In a real app, this would remove the chat from active chats
        console.log(`Closing the current chat: ${activeRoom?.name}`);
        break;
      case 'exit':
        // In a real app, this would call an API to leave the group/page
        console.log(`Exiting the ${activeRoom?.isGroup ? 'group' : 'page'}: ${activeRoom?.name}`);
        break;
    }
  };
  
  // Handle exit group/page from info drawer
  const handleExitFromDrawer = () => {
    setShowInfoDrawer(false);
    // In a real app, call API to exit group/page
    console.log(`Exiting the ${activeRoom?.isGroup ? 'group' : 'page'}: ${activeRoom?.name}`);
  };

  // Toggle dropdown menu
  const toggleDropdown = () => {
    setDropdownOpen(!dropdownOpen);
  };
  
  // Close dropdown when clicking outside or when menu option is selected
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      if (dropdownOpen && !target.closest('.dropdown-toggle')) {
        setDropdownOpen(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [dropdownOpen]);
  
  // Close dropdown automatically after a short delay when an option is selected
  const handleMenuSelect = (action: string) => {
    setDropdownOpen(false);
    handleMenuAction(action);
  };

  if (!activeRoom) {
    return (
      <div className={`flex-1 flex items-center justify-center ${isDark ? 'bg-[#252B42] text-gray-300' : 'bg-white text-gray-500'}`}>
        <p>Select a chat to start messaging</p>
      </div>
    );
  }

  return (
    <>
    <div className={`flex-1 flex flex-col h-full relative ${isDark ? 'bg-[#252B42] text-white' : 'bg-white text-gray-800'}`}>
      {/* Header */}
      <div className={`${isDark ? 'bg-[#252B42] border-[#384058]' : 'bg-white border-gray-200'} border-b p-3 py-2 flex items-center justify-between`}>
        <div className="flex items-center">
          <img 
            src={activeRoom.avatar} 
            alt={activeRoom.name} 
            className="w-10 h-10 rounded-full object-cover shadow-md border border-gray-200"
            onError={(e) => {
              e.currentTarget.src = '/logo.png';
            }}
          />
          <div className="ml-3">
            <h2 className={`text-lg font-medium ${isDark ? 'text-white' : 'text-gray-900'}`}>{activeRoom.name}</h2>
            <div className={`flex items-center text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
              <span className="inline-block w-2 h-2 bg-[#006060] rounded-full mr-1"></span>
              Online
              {activeRoom.isGroup && (
                <span className="ml-2">• {activeRoom.participants.length} participants</span>
              )}
            </div>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <button 
            className={`${isDark ? 'text-gray-300 hover:text-white' : 'text-gray-500 hover:text-gray-700'} p-1.5 rounded-full ${isSearchActive ? `bg-[${themeColors.primary}]/10` : ''}`}
            onClick={() => setIsSearchActive(!isSearchActive)}
            aria-label="Search in conversation"
            title="Search in conversation"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </button>
          <div className="relative dropdown-toggle">
            <button 
              className={`${isDark ? 'text-gray-300 hover:text-white' : 'text-gray-500 hover:text-gray-700'} p-1.5 rounded-full ${dropdownOpen ? `bg-[${themeColors.primary}]/10` : ''}`}
              onClick={toggleDropdown}
              aria-label="Menu"
              title="Menu"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
              </svg>
            </button>
            {dropdownOpen && (
              <div className={`absolute right-0 mt-2 w-52 ${isDark ? 'bg-[#2d3445] text-gray-200' : 'bg-white text-gray-700'} rounded-md shadow-lg z-10 border ${isDark ? 'border-[#384058]' : 'border-gray-200'}`}>
                <div className="py-1">
                  <button 
                    onClick={() => handleMenuSelect('info')} 
                    className={`block w-full text-left px-4 py-2 text-sm ${isDark ? 'hover:bg-[#1e232e]' : 'hover:bg-gray-100'}`}
                  >
                    {activeRoom.isGroup ? 'Group information' : 'Page information'}
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Search Bar - Now floating above messages instead of pushing them down */}
      {isSearchActive && (
        <div className={`fixed top-14 right-28 right-0 max-w-[20%] z-50 px-4 py-3 ${isDark ? 'bg-[#2d3445] border-[#384058]' : 'bg-white border-gray-200'} border rounded-lg shadow-lg`}>
          <div className="relative">
            <input
              type="text"
              placeholder="Search in conversation..."
              className={`w-50px py-1.5 pl-8 pr-8 ${isDark ? 'bg-[#252B42] border-[#384058] text-white placeholder-gray-400' : 'bg-white border-gray-200 text-gray-900'} border rounded-lg focus:outline-none focus:ring-1 focus:ring-[#006060] text-sm`}
              value={searchQuery}
              onChange={handleSearch}
              autoFocus
            />
            <svg
              className={`absolute left-2.5 top-1/2 h-4 w-4 ${isDark ? 'text-gray-400' : 'text-gray-500'} -translate-y-1/2`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            <button
              className={`absolute right-2 top-1/2 -translate-y-1/2 ${isDark ? 'text-gray-400 hover:text-white' : 'text-gray-500 hover:text-gray-700'}`}
              onClick={() => {
                setIsSearchActive(false);
                setSearchQuery('');
                searchMessages(state.activeRoomId || '', '');
              }}
              title="Close search"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          {searchQuery && (
            <div className="mt-2 text-xs">
              <span className={isDark ? 'text-gray-300' : 'text-gray-600'}>
                Searching for: <span className="font-medium">{searchQuery}</span>
              </span>
            </div>
          )}
        </div>
      )}
      
      {/* Messages */}
      <div className={`flex-1 p-4 overflow-y-auto min-h-0 ${isDark ? 'bg-[#252B42]' : 'bg-white'}`}>
        {messages.map((message, index) => {
          const isCurrentUser = message.senderId === '101'; // Using the dummy current user ID
          const showAvatar = index === 0 || messages[index - 1]?.senderId !== message.senderId;
          const showDate = index === 0 || 
            new Date(message.timestamp).toDateString() !== new Date(messages[index - 1]?.timestamp).toDateString();
          const time = new Date(message.timestamp).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
          
          return (
            <div key={message.id} className="mb-4">
              {/* Date separator */}
              {showDate && (
                <div className="flex justify-center mb-3">
                  <span className={`text-xs py-1 px-3 rounded-full ${isDark ? 'bg-gray-700 text-gray-300' : 'bg-gray-100 text-gray-500'}`}>
                    {new Date(message.timestamp).toLocaleDateString()}
                  </span>
                </div>
              )}
              
              {/* Message bubble with timestamps */}
              <div className="mb-1">
                {/* Time and sender name */}
                <div className={`flex justify-${isCurrentUser ? 'end' : 'start'} mb-1`}>
                  <span className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                    {!isCurrentUser && showAvatar ? `${message.senderName} • ` : ""}{time}
                  </span>
                </div>
                
                {/* Message with avatar */}
                <div className={`flex items-end gap-2 ${isCurrentUser ? 'justify-end' : 'justify-start'}`}>
                  {/* Other user's avatar */}
                  {!isCurrentUser && showAvatar && (
                    <div className="flex-shrink-0">
                      <img 
                        src={message.senderAvatar} 
                        alt={message.senderName}
                        className="w-8 h-8 rounded-full object-cover shadow-sm border border-gray-200"
                        onError={(e) => {
                          e.currentTarget.src = '/logo.png';
                        }}
                      />
                    </div>
                  )}
                  
                  {/* Message bubble */}
                  <div className={`px-4 py-2 rounded-2xl max-w-[75%] ${isCurrentUser 
                    ? 'bg-[#006060] text-white' 
                    : isDark ? 'bg-[#2d3445] text-white' : 'bg-gray-100 text-gray-900'}`}
                  >
                    <p className="break-words">{message.content}</p>
                  </div>
                  
                  {/* Current user's avatar */}
                  {isCurrentUser && showAvatar && (
                    <div className="flex-shrink-0">
                      <img 
                        src={message.senderAvatar} 
                        alt={message.senderName}
                        className="w-8 h-8 rounded-full object-cover shadow-sm border border-gray-200"
                        onError={(e) => {
                          e.currentTarget.src = '/logo.png';
                        }}
                      />
                    </div>
                  )}
                </div>
              </div>
            </div>
          );
        })}
        <div ref={messagesEndRef} />
      </div>
      
      {/* Info Drawer */}
      {showInfoDrawer && (
        <div className="absolute top-0 right-0 bottom-0 z-30 flex">
          {/* Drawer */}
          <div 
            className={`w-80 h-full ${isDark ? 'bg-[#252B42] text-white' : 'bg-white text-gray-800'} shadow-lg flex flex-col animate-slide-in-right border-l ${isDark ? 'border-[#384058]' : 'border-gray-200'}`}
            style={{ animationDuration: '250ms' }}
          >
            {/* Drawer Header */}
            <div className={`px-4 py-3 flex items-center border-b ${isDark ? 'border-[#384058]' : 'border-gray-200'}`}>
              <button 
                onClick={() => setShowInfoDrawer(false)}
                className={`p-1 rounded-full ${isDark ? 'hover:bg-[#1e232e]' : 'hover:bg-gray-100'} mr-2`}
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              <h2 className="text-lg font-medium">{activeRoom?.isGroup ? 'Group Info' : 'Page Info'}</h2>
            </div>
            
            {/* Group/Page Avatar and Name */}
            <div className={`p-4 border-b ${isDark ? 'border-[#384058]' : 'border-gray-200'} flex flex-col items-center`}>
              <img 
                src={activeRoom?.avatar} 
                alt={activeRoom?.name} 
                className={`w-20 h-20 rounded-full object-cover shadow-md border ${isDark ? 'border-[#384058]' : 'border-gray-200'}`}
                onError={(e) => {
                  e.currentTarget.src = '/logo.png';
                }}
              />
              <h3 className="mt-3 text-lg font-medium">{activeRoom?.name}</h3>
              <p className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                {activeRoom?.isGroup ? `${activeRoom?.participants.length} participants` : 'Page'}
              </p>
            </div>
            
            {/* Participants (for groups) */}
            {activeRoom?.isGroup && (
              <div className="flex-1 overflow-y-auto p-2 scrollbar-thin">
                <h4 className={`px-2 py-1 text-xs font-medium ${isDark ? 'text-gray-400' : 'text-gray-500'} uppercase`}>
                  {activeRoom?.participants.length} Participants
                </h4>
                
                <div className="mt-2">
                  {activeRoom?.participants.map(participant => (
                    <div 
                      key={participant.id} 
                      className={`flex items-center px-2 py-2 rounded-lg ${isDark ? 'hover:bg-[#1e232e]' : 'hover:bg-gray-50'}`}
                    >
                      <img 
                        src={participant.avatar} 
                        alt={participant.name} 
                        className={`w-10 h-10 rounded-full object-cover mr-3 border ${isDark ? 'border-[#384058]' : 'border-gray-200'}`}
                        onError={(e) => {
                          e.currentTarget.src = '/logo.png';
                        }}
                      />
                      <div>
                        <p className="font-medium">{participant.name}</p>
                        <p className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>{'Member'}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
            
            {/* Exit Button */}
            <div className={`p-4 border-t ${isDark ? 'border-[#384058]' : 'border-gray-200'}`}>
              <button 
                onClick={handleExitFromDrawer}
                className="w-full py-2 px-4 rounded-lg bg-red-500 hover:bg-red-600 text-white font-medium text-sm transition-colors duration-200"
              >
                {activeRoom?.isGroup ? 'Exit Group' : 'Exit Page'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>

    <style jsx>{`
      @keyframes slide-in-right {
        from {
          transform: translateX(100%);
        }
        to {
          transform: translateX(0);
        }
      }
      
      .animate-slide-in-right {
        animation: slide-in-right 0.25s ease-out;
      }

      /* Custom scrollbar styles */
      .scrollbar-thin::-webkit-scrollbar {
        width: 4px;
      }
      
      .scrollbar-thin::-webkit-scrollbar-track {
        background: transparent;
      }
      
      .scrollbar-thin::-webkit-scrollbar-thumb {
        background-color: rgba(156, 163, 175, 0.5);
        border-radius: 20px;
      }
    `}</style>
    </>
  );
};
