'use client';

import React, { useState, useEffect } from 'react';
import { useChat } from '../_logics/chat_context';
import { useTeacherTheme } from '@/components/general/TeacherTheme/TeachersTheme';
import { ChatParticipant } from '../_logics/types';

interface ChatGroupInfoProps {
  isOpen: boolean;
  onClose: () => void;
  chatGroupId: string;
}

export const ChatGroupInfo: React.FC<ChatGroupInfoProps> = ({
  isOpen,
  onClose,
  chatGroupId,
}) => {
  const { state, loadChatGroupMembers, getChatGroupById } = useChat();
  const { isDark } = useTeacherTheme();
  const [groupDetails, setGroupDetails] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const activeRoom = state.rooms.find(room => room.id === chatGroupId);

  useEffect(() => {
    if (isOpen && chatGroupId) {
      loadGroupData();
    }
  }, [isOpen, chatGroupId]);

  const loadGroupData = async () => {
    setLoading(true);
    try {
      // Load group details
      const details = await getChatGroupById(chatGroupId);
      setGroupDetails(details);

      // Load group members
      await loadChatGroupMembers(chatGroupId);
    } catch (error) {
      console.error('Failed to load group data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className={`${isDark ? 'bg-[#252B42] text-white' : 'bg-white text-gray-800'} rounded-lg shadow-xl w-full max-w-md mx-4 max-h-[80vh] overflow-hidden`}>
        {/* Header */}
        <div className={`px-6 py-4 border-b ${isDark ? 'border-[#384058]' : 'border-gray-200'} flex items-center justify-between`}>
          <h2 className="text-lg font-semibold">Group Info</h2>
          <button
            onClick={onClose}
            className={`p-2 rounded-full hover:${isDark ? 'bg-[#384058]' : 'bg-gray-100'} transition-colors`}
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="overflow-y-auto max-h-[calc(80vh-120px)]">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[#006060]"></div>
            </div>
          ) : (
            <>
              {/* Group Details */}
              <div className="px-6 py-4">
                <div className="flex items-center mb-4">
                  <img
                    src="/logo.png"
                    alt={activeRoom?.name || 'Group'}
                    className="w-16 h-16 rounded-full object-cover shadow-md border border-gray-200"
                  />
                  <div className="ml-4">
                    <h3 className="text-xl font-semibold">{activeRoom?.name || 'Unknown Group'}</h3>
                    <p className={`text-sm ${isDark ? 'text-gray-300' : 'text-gray-500'}`}>
                      {activeRoom?.participants?.length || 0} members
                    </p>
                  </div>
                </div>

                {groupDetails && (
                  <div className="space-y-2">
                    {groupDetails.description && (
                      <div>
                        <p className={`text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>Description</p>
                        <p className={`text-sm ${isDark ? 'text-gray-200' : 'text-gray-700'}`}>{groupDetails.description}</p>
                      </div>
                    )}
                    <div>
                      <p className={`text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>Type</p>
                      <p className={`text-sm ${isDark ? 'text-gray-200' : 'text-gray-700'}`}>
                        {groupDetails.type === 'course' ? 'Course Group' : 'Custom Group'}
                      </p>
                    </div>
                    <div>
                      <p className={`text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>Privacy</p>
                      <p className={`text-sm ${isDark ? 'text-gray-200' : 'text-gray-700'}`}>
                        {groupDetails.is_private ? 'Private' : 'Public'}
                      </p>
                    </div>
                  </div>
                )}
              </div>

              {/* Members List */}
              <div className={`px-6 py-4 border-t ${isDark ? 'border-[#384058]' : 'border-gray-200'}`}>
                <h4 className="text-lg font-semibold mb-3">Members</h4>
                <div className="space-y-3">
                  {activeRoom?.participants?.map((participant: ChatParticipant) => (
                    <div key={participant.id} className="flex items-center">
                      <img
                        src={participant.avatar}
                        alt={participant.name}
                        className="w-10 h-10 rounded-full object-cover shadow-sm border border-gray-200"
                        onError={(e) => {
                          e.currentTarget.src = '/logo.png';
                        }}
                      />
                      <div className="ml-3">
                        <p className={`text-sm font-medium ${isDark ? 'text-white' : 'text-gray-800'}`}>
                          {participant.name}
                        </p>
                        <p className={`text-xs ${isDark ? 'text-gray-300' : 'text-gray-500'}`}>
                          Member
                        </p>
                      </div>
                    </div>
                  )) || (
                    <p className={`text-sm ${isDark ? 'text-gray-300' : 'text-gray-500'}`}>
                      No members loaded yet.
                    </p>
                  )}
                </div>
              </div>
            </>
          )}
        </div>

        {/* Footer */}
        <div className={`px-6 py-4 border-t ${isDark ? 'border-[#384058]' : 'border-gray-200'} flex justify-end`}>
          <button
            onClick={onClose}
            className={`px-4 py-2 rounded-lg ${isDark ? 'bg-[#384058] hover:bg-[#4a5568] text-white' : 'bg-gray-100 hover:bg-gray-200 text-gray-800'} transition-colors`}
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};
