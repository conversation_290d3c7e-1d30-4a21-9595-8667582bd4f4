'use client';

import React, { useState, useRef, useEffect } from 'react';
import { useChat } from '../_logics/chat_context';
import { useTeacherTheme } from '@/components/general/TeacherTheme/TeachersTheme';
import dynamic from 'next/dynamic';

// Importing type for TS checking, but using dynamic import for the component
import type { Theme } from 'emoji-picker-react';

// Dynamically import EmojiPicker to avoid SSR issues
const EmojiPicker = dynamic(() => import('emoji-picker-react'), { ssr: false });

export const ChatMessageInput = () => {
  const [message, setMessage] = useState('');
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const emojiPickerRef = useRef<HTMLDivElement>(null);
  const emojiButtonRef = useRef<HTMLButtonElement>(null);
  const { sendMessage } = useChat();
  const { isDark } = useTeacherTheme();



  // Handle emoji selection
  const handleEmojiClick = (emojiData: any) => {
    // Different versions of emoji-picker-react have different response formats
    // This approach handles multiple versions
    const emoji = emojiData.emoji || (emojiData.srcElement && emojiData.srcElement.innerText) || '';
    setMessage(prevMessage => prevMessage + emoji);
  };

  // Close emoji picker when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        emojiPickerRef.current &&
        !emojiPickerRef.current.contains(event.target as Node) &&
        emojiButtonRef.current &&
        !emojiButtonRef.current.contains(event.target as Node)
      ) {
        setShowEmojiPicker(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (message.trim()) {
      sendMessage(message);
      setMessage('');
      setShowEmojiPicker(false);
    }
  };

  return (
    <div className={`${isDark ? 'bg-[#252B42] border-[#384058]' : 'bg-white border-gray-200'} border-t py-2.5 px-4`}>
      <form onSubmit={handleSubmit} className="flex items-center space-x-2">
        <div className="relative">
          <button
            ref={emojiButtonRef}
            type="button"
            className="text-gray-400 hover:text-gray-600 p-2 rounded-full"
            title="Emoji"
            onClick={() => setShowEmojiPicker(!showEmojiPicker)}
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </button>

          {showEmojiPicker && (
            <div
              ref={emojiPickerRef}
              className="absolute bottom-12 left-0 z-10"
            >
              <EmojiPicker
                onEmojiClick={handleEmojiClick}
                theme={(isDark ? "dark" : "light") as Theme}
                autoFocusSearch={false}
              />
            </div>
          )}
        </div>
        <div className={`flex-1 flex items-center py-2 px-4 border ${isDark ?
          'bg-[#2d3445] border-[#384058]' :
          'bg-gray-100 border-gray-200'} rounded-full focus-within:ring-1 focus-within:ring-[#006060]`}>
          <input
            type="text"
            placeholder="Type message..."
            className={`flex-1 bg-transparent border-none p-0 ${isDark ?
              'text-white placeholder-gray-400' :
              'text-gray-900 placeholder-gray-500'} focus:outline-none text-sm`}
            value={message}
            onChange={(e) => setMessage(e.target.value)}
          />
          <button
            type="button"
            className="text-gray-400 hover:text-gray-600 ml-1"
            title="Attach file"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
            </svg>
          </button>
        </div>
        <button
          type="submit"
          className="bg-[#006060] hover:bg-[#004645] text-white p-3 rounded-full transition-colors duration-200 flex items-center justify-center"
          disabled={!message.trim()}
          title="Send message"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
          </svg>
        </button>
      </form>
    </div>
  );
};
