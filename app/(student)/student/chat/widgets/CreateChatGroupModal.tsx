'use client';

import React, { useState } from 'react';
import { ChatApiService } from '../_logics/chat_api_service';
import { useTeacherTheme } from '@/components/general/TeacherTheme/TeachersTheme';

interface CreateChatGroupModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export const CreateChatGroupModal: React.FC<CreateChatGroupModalProps> = ({ 
  isOpen, 
  onClose,
  onSuccess
}) => {
  const { isDark } = useTeacherTheme();
  const [groupName, setGroupName] = useState('');
  const [description, setDescription] = useState('');
  const [isPrivate, setIsPrivate] = useState(false);
  const [selectedCourseId, setSelectedCourseId] = useState('');
  const [selectedMembers, setSelectedMembers] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  
  const chatApiService = ChatApiService.getInstance();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!groupName.trim()) {
      setError('Group name is required');
      return;
    }
    
    try {
      setIsSubmitting(true);
      setError('');
      
      // Create chat group with the exact format provided in the example
      const groupData = {
        name: groupName,
        description: description,
        type: selectedCourseId ? 'course' : 'student', 
        course_id: selectedCourseId || '', 
        is_private: isPrivate,
        initial_members: selectedMembers
      };
      
      console.log('Creating chat group with data:', groupData);
      await chatApiService.createChatGroup(groupData);
      
      // Reset form
      setGroupName('');
      setDescription('');
      setIsPrivate(false);
      setSelectedCourseId('');
      setSelectedMembers([]);
      
      // Notify parent component of success
      onSuccess();
      onClose();
    } catch (error) {
      console.error('Error creating chat group:', error);
      setError('Failed to create chat group. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };
  
  if (!isOpen) return null;
  
  return (
    <div className="fixed inset-0 flex items-center justify-center z-50">
      <div className="absolute inset-0 bg-black opacity-50" onClick={onClose}></div>
      
      <div className={`relative w-full max-w-md p-6 rounded-lg shadow-lg ${isDark ? 'bg-[#1e232e] text-white' : 'bg-white text-gray-800'}`}>
        <h2 className="text-xl font-semibold mb-4">Create New Chat Group</h2>
        
        {error && (
          <div className="mb-4 p-2 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
          </div>
        )}
        
        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label className="block text-sm font-medium mb-1">Group Name*</label>
            <input
              type="text"
              value={groupName}
              onChange={(e) => setGroupName(e.target.value)}
              className={`w-full px-3 py-2 border rounded-md ${isDark ? 'bg-[#252B42] border-gray-700' : 'bg-white border-gray-300'}`}
              placeholder="Enter group name"
              required
            />
          </div>
          
          <div className="mb-4">
            <label className="block text-sm font-medium mb-1">Description</label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className={`w-full px-3 py-2 border rounded-md ${isDark ? 'bg-[#252B42] border-gray-700' : 'bg-white border-gray-300'}`}
              placeholder="Enter group description"
              rows={3}
            ></textarea>
          </div>
          
          <div className="mb-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={isPrivate}
                onChange={(e) => setIsPrivate(e.target.checked)}
                className="mr-2"
              />
              <span className="text-sm">Private Group</span>
            </label>
          </div>
          
          {/* Course selection */}
          <div className="mb-4">
            <label className="block text-sm font-medium mb-1">Select Course (Optional)</label>
            <select
              value={selectedCourseId}
              onChange={(e) => setSelectedCourseId(e.target.value)}
              className={`w-full px-3 py-2 border rounded-md ${isDark ? 'bg-[#252B42] border-gray-700' : 'bg-white border-gray-300'}`}
            >
              <option value="">None (General Group)</option>
              <option value="2c8f3470-6f2c-4df4-97a3-c8c51430f696">Flutter Mobile Development</option>
              <option value="3a7d1b59-9e47-4eb1-a32c-85c4b3e6e8f1">Web Development</option>
              <option value="5b8c2d45-1234-5678-9abc-def012345678">Data Science</option>
            </select>
          </div>
          
          {/* Member selection */}
          <div className="mb-4">
            <label className="block text-sm font-medium mb-1">Add Members (Optional)</label>
            <div className="space-y-2">
              {['1', '2', '3'].map(userId => (
                <label key={userId} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={selectedMembers.includes(userId)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedMembers([...selectedMembers, userId]);
                      } else {
                        setSelectedMembers(selectedMembers.filter(id => id !== userId));
                      }
                    }}
                    className="mr-2"
                  />
                  <span className="text-sm">User {userId}</span>
                </label>
              ))}
            </div>
          </div>
          
          <div className="flex justify-end space-x-2 mt-6">
            <button
              type="button"
              onClick={onClose}
              className={`px-4 py-2 rounded-md ${isDark ? 'bg-gray-700 hover:bg-gray-600' : 'bg-gray-200 hover:bg-gray-300'}`}
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-[#006060] text-white rounded-md hover:bg-[#004e4e] disabled:opacity-50"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Creating...' : 'Create Group'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};
