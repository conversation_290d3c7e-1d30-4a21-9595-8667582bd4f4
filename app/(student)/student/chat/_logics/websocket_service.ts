import { ChatMessage, WebSocketMessage } from './types';
import { getCurrentUser } from './chat_api_service';

// Format WebSocket message to match our ChatMessage format
export const formatWebSocketMessage = (wsMessage: WebSocketMessage): ChatMessage => {
  return {
    id: wsMessage.id || wsMessage._id || `${Date.now()}-${Math.random()}`,
    senderId: wsMessage.sender_id || wsMessage.sender?.id || 'unknown',
    senderName: wsMessage.sender_name || wsMessage.sender?.name || 'Unknown User',
    senderAvatar: wsMessage.sender_avatar || wsMessage.sender?.avatar || '/logo.png',
    content: wsMessage.content || '',
    timestamp: new Date(wsMessage.created_at || wsMessage.timestamp || Date.now())
  };
};

/**
 * Singleton WebSocket Service for chat
 * Supports three WebSocket endpoints:
 * 1. /ws/chat/{room_id} - Connect to a chat room
 * 2. /ws/chat/direct/{recipient_id} - Send and receive direct chat messages
 * 3. /ws/notifications/{user_id} - Receive real-time notifications
 */
export class ChatWebSocketService {
  private static instance: ChatWebSocketService;
  private socket: WebSocket | null = null;
  private notificationsSocket: WebSocket | null = null;
  private connectionStateChangeCallbacks: ((connected: boolean) => void)[] = [];
  private messageCallbacks: ((message: WebSocketMessage) => void)[] = [];
  private currentRoom: string | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private reconnectTimeout: NodeJS.Timeout | null = null;
  private heartbeatIntervalMs = 30000;


  private constructor() {}

  public static getInstance(): ChatWebSocketService {
    if (!ChatWebSocketService.instance) {
      ChatWebSocketService.instance = new ChatWebSocketService();
    }
    return ChatWebSocketService.instance;
  }

  /**
   * Connect to a chat room using WebSocket
   * @param roomId The ID of the chat room to connect to
   */
  public connectToRoom(roomId: string): void {
    this.disconnect();
    this.currentRoom = roomId;

    console.log(`Connecting to chat room: ${roomId}`);

    try {
      const user = getCurrentUser();
      if (!user) {
        console.error('No user found for WebSocket authentication');
        return;
      }

      const userId = user.student_id || user.teacher_id || user.id || '';
      if (!userId) {
        console.error('No valid user ID found for WebSocket connection');
        return;
      }

      // Create WebSocket connection to the chat room
      // Use environment variable for WebSocket base URL
      console.log(`Connecting to chat room with ID: ${roomId} and user ID: ${userId}`);
      const wsBaseUrl = process.env.NEXT_PUBLIC_WS_CHAT_BASE_URL!;
      const wsUrl = `${wsBaseUrl}/${roomId}?user_id=${userId}`;

      console.log(`Connecting to WebSocket URL: ${wsUrl}`);

      this.socket = new WebSocket(wsUrl);
      this.setupEventHandlers();
      this.startHeartbeat();
    } catch (error) {
      console.error('Error connecting to WebSocket:', error);
      this.notifyConnectionStateChange(false);
    }
  }

  /**
   * Connect to direct chat with another user
   * @param recipientId The ID of the recipient to chat with
   */
  public connectToDirectChat(recipientId: string): void {
    this.disconnect();
    this.currentRoom = `direct-${recipientId}`;

    console.log(`Connecting to direct chat with recipient: ${recipientId}`);

    try {
      const user = getCurrentUser();
      if (!user) {
        console.error('No user found for WebSocket authentication');
        return;
      }

      const userId = user.student_id || user.teacher_id || user.id || '';
      if (!userId) {
        console.error('No valid user ID found for WebSocket connection');
        return;
      }

      // Create WebSocket connection to direct chat
      // Use environment variable for WebSocket base URL
      console.log(`Connecting to direct chat with recipient ID: ${recipientId} and user ID: ${userId}`);
      const wsBaseUrl = process.env.NEXT_PUBLIC_WS_CHAT_BASE_URL!;
      const wsUrl = `${wsBaseUrl}/direct/${recipientId}?user_id=${userId}`;

      console.log(`Connecting to WebSocket URL: ${wsUrl}`);

      this.socket = new WebSocket(wsUrl);
      this.setupEventHandlers();
      this.startHeartbeat();
    } catch (error) {
      console.error('Error connecting to WebSocket:', error);
      this.notifyConnectionStateChange(false);
    }
  }

  /**
   * Connect to a specific chat group by ID (for the pattern like 2c8f3470-6f2c-4df4-97a3-c8c51430f696)
   * This method ensures authentication before connecting
   * @param chatGroupId - The specific chat group ID
   */
  public connectToSpecificChatGroup(chatGroupId: string): void {
    try {
      // Get the current user for authentication
      const user = getCurrentUser();
      if (!user) {
        console.error('No authenticated user found - cannot connect to chat group');
        this.notifyConnectionStateChange(false);
        return;
      }

      const userId = user.student_id || user.teacher_id || user.id || '';
      if (!userId) {
        console.error('No valid user ID found for WebSocket authentication');
        this.notifyConnectionStateChange(false);
        return;
      }

      console.log(`Connecting to specific chat group ${chatGroupId} as user ${userId}`);

      // Use the same connectToRoom method but with explicit logging for specific chat groups
      this.connectToRoom(chatGroupId);
    } catch (error) {
      console.error(`Error connecting to specific chat group ${chatGroupId}:`, error);
      this.notifyConnectionStateChange(false);
    }
  }

  /**
   * Connect to notifications channel for real-time updates
   */
  public connectToNotifications(): void {
    try {
      const user = getCurrentUser();
      if (!user) {
        console.error('No user found for WebSocket authentication');
        return;
      }

      const userId = user.student_id || user.teacher_id || user.id || '';
      if (!userId) {
        console.error('No valid user ID found for WebSocket connection');
        return;
      }

      // Create WebSocket connection to notifications
      // Use environment variable for notifications WebSocket
      console.log(`Connecting to notifications for user ID: ${userId}`);
      const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL!;
      const wsUrl = `${apiBaseUrl.replace('http', 'ws')}/ws/notifications/${userId}`;

      console.log(`Connecting to notifications WebSocket: ${wsUrl}`);

      // Create a separate WebSocket connection for notifications
      this.notificationsSocket = new WebSocket(wsUrl);

      this.notificationsSocket.onopen = () => {
        console.log('Notifications WebSocket connection established');
      };

      this.notificationsSocket.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          console.log('Notification received:', data);

          // Handle different types of notifications here
          // For example, new messages, new group invitations, etc.
        } catch (error) {
          console.error('Error processing notification:', error);
        }
      };

      this.notificationsSocket.onerror = (error: Event) => {
        console.error('Notifications WebSocket error:', error);
      };

      this.notificationsSocket.onclose = () => {
        console.log('Notifications WebSocket connection closed');
        // Reconnect after a delay
        setTimeout(() => this.connectToNotifications(), 5000);
      };

    } catch (error) {
      console.error('Error connecting to notifications WebSocket:', error);
      // Retry after a delay
      setTimeout(() => this.connectToNotifications(), 5000);
    }
  }

  /**
   * Connect to a group chat room (alias for connectToRoom)
   * @param roomId The ID of the group chat room to connect to
   */
  public connectToGroupChat(roomId: string): void {
    this.connectToRoom(roomId);
  }

  /**
   * Send a message to a group chat
   * @param content The message content to send
   */
  public sendGroupMessage(content: string): void {
    this.sendMessage(content);
  }

  /**
   * Send a direct message to a specific recipient
   * @param recipientId The ID of the recipient
   * @param content The message content to send
   */
  public sendDirectMessage(recipientId: string, content: string): void {
    if (!this.socket || this.socket.readyState !== WebSocket.OPEN) {
      console.error('Cannot send direct message: WebSocket not connected');
      return;
    }

    try {
      const user = getCurrentUser();
      if (!user) {
        console.error('Cannot send direct message: No user found');
        return;
      }

      const message = {
        type: 'direct_message',
        content,
        recipient_id: recipientId,
        timestamp: new Date().toISOString(),
        sender: {
          id: user.student_id || user.teacher_id || user.id || '',
          name: user.name || 'Unknown User',
          avatar: user.avatar || '/logo.png'
        }
      };

      this.socket.send(JSON.stringify(message));
    } catch (error) {
      console.error('Error sending direct message:', error);
    }
  }

  /**
   * Send a message to the current chat room
   * @param content The message content to send
   */
  public sendMessage(content: string): void {
    if (!this.socket || this.socket.readyState !== WebSocket.OPEN) {
      console.error('Cannot send message: WebSocket not connected');
      return;
    }

    try {
      const user = getCurrentUser();
      if (!user) {
        console.error('Cannot send message: No user found');
        return;
      }

      const message = {
        type: 'message',
        content,
        timestamp: new Date().toISOString(),
        sender: {
          id: user.student_id || user.teacher_id || user.id || '',
          name: user.name || 'Unknown User',
          avatar: user.avatar || '/logo.png'
        }
      };

      this.socket.send(JSON.stringify(message));
    } catch (error) {
      console.error('Error sending message:', error);
    }
  }

  /**
   * Set up WebSocket event handlers
   */
  private setupEventHandlers(): void {
    if (!this.socket) return;

    this.socket.onopen = () => {
      console.log('WebSocket connection established');
      this.notifyConnectionStateChange(true);
      this.reconnectAttempts = 0; // Reset reconnect attempts on successful connection
    };

    this.socket.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        console.log('Message received:', data);
        this.messageCallbacks.forEach(callback => callback(data));
      } catch (error) {
        console.error('Error processing message:', error);
      }
    };

    this.socket.onerror = (error: Event) => {
      console.error('WebSocket error:', error);
      this.notifyConnectionStateChange(false);
    };

    this.socket.onclose = () => {
      console.log('WebSocket connection closed');
      this.notifyConnectionStateChange(false);

      // Clear heartbeat interval
      if (this.heartbeatInterval) {
        clearInterval(this.heartbeatInterval);
        this.heartbeatInterval = null;
      }

      // Attempt to reconnect if we haven't exceeded the maximum number of attempts
      if (this.reconnectAttempts < this.maxReconnectAttempts) {
        this.startReconnect();
      }
    };
  }

  /**
   * Disconnect from WebSocket connections
   */
  public disconnect(): void {
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }

    // Clear intervals and timeouts
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }

    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }

    this.currentRoom = null;
  }

  /**
   * Close the notifications WebSocket connection
   */
  public disconnectNotifications(): void {
    if (this.notificationsSocket) {
      this.notificationsSocket.close();
      this.notificationsSocket = null;
    }
  }

  /**
   * Register a callback to be notified when connection state changes
   * @param callback The callback function to be called when connection state changes
   */
  public onConnectionStateChange(callback: (connected: boolean) => void): void {
    this.connectionStateChangeCallbacks.push(callback);
  }

  /**
   * Register a callback to be notified when a message is received
   * @param callback The callback function to be called when a message is received
   */
  public onMessage(callback: (message: WebSocketMessage) => void): void {
    this.messageCallbacks.push(callback);
  }

  /**
   * Notify all registered callbacks of a connection state change
   * @param connected Whether the connection is established
   */
  private notifyConnectionStateChange(connected: boolean): void {
    this.connectionStateChangeCallbacks.forEach(callback => callback(connected));
  }

  /**
   * Start the heartbeat interval to keep the connection alive
   */
  private startHeartbeat(): void {
    // Clear any existing heartbeat
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }

    // Set up a new heartbeat interval
    this.heartbeatInterval = setInterval(() => {
      if (this.socket && this.socket.readyState === WebSocket.OPEN) {
        // Send a ping message to keep the connection alive
        try {
          this.socket.send(JSON.stringify({ type: 'ping' }));
          console.log('Sent heartbeat ping');
        } catch (error) {
          console.error('Error sending heartbeat:', error);
        }
      }
    }, this.heartbeatIntervalMs);
  }

  /**
   * Start the reconnection process
   */
  private startReconnect(): void {
    if (this.reconnectTimeout) {
      return;
    }

    this.reconnectAttempts++;

    // Exponential backoff for reconnect
    const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);

    console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

    this.reconnectTimeout = setTimeout(() => {
      this.reconnectTimeout = null;

      // Try to reconnect
      if (this.currentRoom) {
        if (this.currentRoom.startsWith('direct-')) {
          const recipientId = this.currentRoom.replace('direct-', '');
          this.connectToDirectChat(recipientId);
        } else {
          this.connectToRoom(this.currentRoom);
        }
      }
    }, delay);
  }
}
