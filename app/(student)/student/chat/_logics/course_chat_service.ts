import { Chat<PERSON>oom } from "./types";
import { getCurrentUser } from "./chat_api_service";

// Service to handle course-specific chat operations
export class CourseGroupService {
  private static instance: CourseGroupService;

  private constructor() {
    // Constructor implementation
  }

  // Helper method to make API requests using axios directly (like other parts of the codebase)
  private async makeRequest(method: "GET" | "POST" | "PUT" | "DELETE", endpoint: string, data?: any, contentType: string = "application/json") {
    const axios = require('axios');
    const Cookies = require('js-cookie');

    const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;
    const token = Cookies.default.get("access_token");

    console.log(`CourseAPI - Making ${method} request to ${endpoint}`);
    console.log(`CourseAPI - Token exists: ${!!token}`);

    try {
      const response = await axios({
        method,
        url: `${API_BASE_URL}${endpoint}`,
        data: data || null,
        headers: {
          'Content-Type': contentType,
          Authorization: token ? `Bearer ${token}` : "",
        },
      });

      console.log(`CourseAPI - ${method} ${endpoint} response status:`, response.status);
      return response;
    } catch (err: any) {
      console.error(`CourseAPI - ${method} ${endpoint} error:`, err);
      return err?.response;
    }
  }

  public static getInstance(): CourseGroupService {
    if (!CourseGroupService.instance) {
      CourseGroupService.instance = new CourseGroupService();
    }
    return CourseGroupService.instance;
  }

  // Fetch all course groups for the current student
  public async fetchStudentCourseGroups(): Promise<ChatRoom[]> {
    try {
      const user = getCurrentUser();
      if (!user) {
        console.error("No authenticated user found");
        return [];
      }

      const userId = user.student_id || user.id;
      if (!userId) {
        console.error("No valid student ID found");
        return [];
      }

      // First try to get existing chat groups for the user using the proper API endpoint
      const response = await this.makeRequest("GET", "/chat-group");

      console.log("Course chat groups response:", response);

      if (response && response.status >= 200 && response.status < 300) {
        // Handle different possible response structures
        let chatGroupsArray = [];

        if (response.data) {
          if (response.data.data && Array.isArray(response.data.data)) {
            chatGroupsArray = response.data.data;
          } else if (response.data.chat_groups && Array.isArray(response.data.chat_groups)) {
            chatGroupsArray = response.data.chat_groups;
          } else if (Array.isArray(response.data)) {
            chatGroupsArray = response.data;
          } else {
            console.warn("Unexpected course chat groups response structure:", response.data);
            return [];
          }
        }

        console.log("Course chat groups array:", chatGroupsArray);

        if (chatGroupsArray && chatGroupsArray.length > 0) {
          // Convert API response to our format
          const formattedGroups = chatGroupsArray.map((group: any) => ({
            id: group.id || group.chat_group_id || `temp-${Date.now()}`,
            name: group.name || group.group_name || "Unnamed Group",
            lastMessage: group.last_message?.content || group.lastMessage || "No messages yet",
            timestamp: new Date(group.updated_at || group.created_at || Date.now()),
            isGroup: true,
            avatar: "/logo.png",
            participants: (group.members || []).map((member: any) => ({
              id: member.student_id || member.teacher_id || member.user_id || member.id || `member-${Date.now()}`,
              name: member.name || member.username || member.first_name || "Unknown User",
              avatar: member.avatar || member.profile_image || "/logo.png",
            })),
          }));

          console.log("Formatted course groups:", formattedGroups);
          return formattedGroups;
        } else {
          console.log("No course chat groups found");
        }
      } else {
        console.error(`Failed to fetch chat groups: ${response?.status}`, response?.data);
      }

      // Return empty array if no chat groups found
      return [];
    } catch (error) {
      console.error("Error fetching course groups:", error);
      return [];
    }
  }



  // Join a course group chat
  public async joinCourseGroup(courseId: string): Promise<boolean> {
    try {
      const user = getCurrentUser();
      if (!user) {
        console.error("No authenticated user found");
        return false;
      }

      const userId = user.student_id || user.id;
      if (!userId) {
        console.error("No valid student ID found");
        return false;
      }

      const response = await this.makeRequest(
        "POST",
        `/chat-group/${courseId}/join`,
        { user_id: userId },
        "application/json"
      );

      return response && response.status >= 200 && response.status < 300;
    } catch (error) {
      console.error(`Error joining course group ${courseId}:`, error);
      return false; // Return false instead of throwing to prevent UI crashes
    }
  }
}
