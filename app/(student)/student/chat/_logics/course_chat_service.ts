import { Cha<PERSON><PERSON><PERSON>, Chat<PERSON>ess<PERSON> } from "./types";
import { getCurrentUser } from "./chat_api_service";

// Service to handle course-specific chat operations
export class CourseGroupService {
  private static instance: CourseGroupService;
  private apiBaseUrl: string = process.env.NEXT_PUBLIC_API_BASE_URL!;

  private constructor() {
    // Constructor implementation
  }

  public static getInstance(): CourseGroupService {
    if (!CourseGroupService.instance) {
      CourseGroupService.instance = new CourseGroupService();
    }
    return CourseGroupService.instance;
  }

  // Fetch all course groups for the current student
  public async fetchStudentCourseGroups(): Promise<ChatRoom[]> {
    try {
      const user = getCurrentUser();
      if (!user) {
        console.error("No authenticated user found");
        return [];
      }

      const userId = user.student_id || user.id;
      if (!userId) {
        console.error("No valid student ID found");
        return [];
      }

      // First try to get existing chat groups for the user using the proper API endpoint
      const response = await fetch(`${this.apiBaseUrl}/chat-group`);

      if (!response.ok) {
        console.error(`Failed to fetch chat groups: ${response.status}`);
      } else {
        const chatGroups = await response.json();
        if (chatGroups && chatGroups.length > 0) {
          // Convert API response to our format
          const formattedGroups = chatGroups.map((group: any) => ({
            id: group.id,
            name: group.name,
            lastMessage: group.last_message || "No messages yet",
            timestamp: new Date(group.updated_at || group.created_at),
            isGroup: true,
            avatar: "/logo.png",
            participants: (group.members || []).map((member: any) => ({
              id: member.id,
              name: member.name || "Unknown User",
              avatar: member.avatar || "/logo.png",
            })),
          }));

          return formattedGroups;
        }
      }

      // Return empty array if no chat groups found
      return [];
    } catch (error) {
      console.error("Error fetching course groups:", error);
      return [];
    }
  }

  // Fetch course group messages
  public async fetchCourseGroupMessages(
    courseId: string
  ): Promise<ChatMessage[]> {
    try {
      const response = await fetch(
        `${this.apiBaseUrl}/chat-group/${courseId}/messages`
      );

      if (!response.ok) {
        throw new Error(`Failed to fetch course messages: ${response.status}`);
      }

      const data = await response.json();
      const messages = data.messages || [];

      // Convert ChatMessage format
      return messages.map((msg: any) => ({
        id: msg.id,
        senderId: msg.sender_id,
        senderName: msg.sender_name,
        senderAvatar: msg.sender_avatar || "/logo.png",
        content: msg.content,
        timestamp: new Date(msg.timestamp),
      }));
    } catch (error) {
      console.error(
        `Error fetching messages for course group ${courseId}:`,
        error
      );
      return [];
    }
  }

  // Join a course group chat
  public async joinCourseGroup(courseId: string): Promise<boolean> {
    try {
      const user = getCurrentUser();
      if (!user) {
        console.error("No authenticated user found");
        return false;
      }

      const userId = user.student_id || user.id;
      if (!userId) {
        console.error("No valid student ID found");
        return false;
      }

      const response = await fetch(
        `${this.apiBaseUrl}/chat-group/${courseId}/join`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ user_id: userId }),
        }
      );

      return response.ok;
    } catch (error) {
      console.error(`Error joining course group ${courseId}:`, error);
      return false; // Return false instead of throwing to prevent UI crashes
    }
  }
}
