import { Chat<PERSON>oom } from "./types";
import { getCurrentUser } from "./chat_api_service";

// Service to handle course-specific chat operations
export class CourseGroupService {
  private static instance: CourseGroupService;

  private constructor() {
    // Constructor implementation
  }

  // Helper method to use the useApi hook
  private async makeRequest(method: "GET" | "POST" | "PUT" | "DELETE", endpoint: string, data?: any, contentType?: string) {
    const { useApi } = require('@/hooks/useRequest');
    const { request } = useApi();
    return await request(method, endpoint, data, contentType);
  }

  public static getInstance(): CourseGroupService {
    if (!CourseGroupService.instance) {
      CourseGroupService.instance = new CourseGroupService();
    }
    return CourseGroupService.instance;
  }

  // Fetch all course groups for the current student
  public async fetchStudentCourseGroups(): Promise<ChatRoom[]> {
    try {
      const user = getCurrentUser();
      if (!user) {
        console.error("No authenticated user found");
        return [];
      }

      const userId = user.student_id || user.id;
      if (!userId) {
        console.error("No valid student ID found");
        return [];
      }

      // First try to get existing chat groups for the user using the proper API endpoint
      const response = await this.makeRequest("GET", "/chat-group");

      if (response && response.status >= 200 && response.status < 300) {
        const chatGroups = response.data;
        if (chatGroups && chatGroups.length > 0) {
          // Convert API response to our format
          const formattedGroups = chatGroups.map((group: any) => ({
            id: group.id,
            name: group.name,
            lastMessage: group.last_message || "No messages yet",
            timestamp: new Date(group.updated_at || group.created_at),
            isGroup: true,
            avatar: "/logo.png",
            participants: (group.members || []).map((member: any) => ({
              id: member.id,
              name: member.name || "Unknown User",
              avatar: member.avatar || "/logo.png",
            })),
          }));

          return formattedGroups;
        }
      } else {
        console.error(`Failed to fetch chat groups: ${response?.status}`);
      }

      // Return empty array if no chat groups found
      return [];
    } catch (error) {
      console.error("Error fetching course groups:", error);
      return [];
    }
  }



  // Join a course group chat
  public async joinCourseGroup(courseId: string): Promise<boolean> {
    try {
      const user = getCurrentUser();
      if (!user) {
        console.error("No authenticated user found");
        return false;
      }

      const userId = user.student_id || user.id;
      if (!userId) {
        console.error("No valid student ID found");
        return false;
      }

      const response = await this.makeRequest(
        "POST",
        `/chat-group/${courseId}/join`,
        { user_id: userId },
        "application/json"
      );

      return response && response.status >= 200 && response.status < 300;
    } catch (error) {
      console.error(`Error joining course group ${courseId}:`, error);
      return false; // Return false instead of throwing to prevent UI crashes
    }
  }
}
