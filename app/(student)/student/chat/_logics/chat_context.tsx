"use client";

import {
  createContext,
  useContext,
  useReducer,
  ReactNode,
  useEffect,
  useState,
} from "react";
import { ChatMessage, ChatRoom, ChatState, ChatGroupCreateRequest } from "./types";
import {
  ChatWebSocketService,
  formatWebSocketMessage,
} from "./websocket_service";
import { CourseGroupService } from "./course_chat_service";
import { ChatApiService, getCurrentUser } from "./chat_api_service";

// Chat context interface for global state management
export interface ChatContextType {
  state: ChatState;
  webSocketConnected: boolean;
  setActiveRoom: (roomId: string) => void;
  sendMessage: (content: string) => Promise<void>;
  searchChats: (query: string) => void;
  searchMessages: (roomId: string, query: string) => void;
  createChatGroup: (
    name: string,
    description: string,
    isPrivate: boolean,
    courseId?: string,
    initialMembers?: string[]
  ) => Promise<boolean>;
  addMemberToChatGroup: (
    chatGroupId: string,
    memberId: string,
    isStudent: boolean,
    isAdmin: boolean
  ) => Promise<boolean>;
  refreshChatRooms: () => Promise<void>;
  joinSpecificChatGroup: (chatGroupId: string) => Promise<boolean>;
  connectToSpecificChatGroup: (chatGroupId: string) => Promise<void>;
  loadChatGroupMembers: (chatGroupId: string) => Promise<void>;
  getChatGroupById: (chatGroupId: string) => Promise<any>;
}

// Initial state
const initialState: ChatState = {
  activeRoomId: null,
  rooms: [],
  messages: {},
  isLoading: false,
  error: null,
  courseGroupsLoaded: false,
};

// Action types
type ActionType =
  | { type: "SET_ACTIVE_ROOM"; payload: string }
  | { type: "SEND_MESSAGE"; payload: { roomId: string; message: ChatMessage } }
  | { type: "SEARCH_CHATS"; payload: string }
  | { type: "SEARCH_MESSAGES"; payload: { roomId: string; query: string } }
  | { type: "SET_LOADING"; payload: boolean }
  | { type: "SET_ERROR"; payload: string | null }
  | { type: "ADD_COURSE_GROUPS"; payload: ChatRoom[] }
  | { type: "SET_CHAT_ROOMS"; payload: ChatRoom[] }
  | {
      type: "SET_CHAT_MESSAGES";
      payload: { roomId: string; messages: ChatMessage[] };
    }
  | { type: "SET_COURSE_GROUPS_LOADED"; payload: boolean };

// Reducer
const chatReducer = (state: ChatState, action: ActionType): ChatState => {
  switch (action.type) {
    case "SET_ACTIVE_ROOM":
      return {
        ...state,
        activeRoomId: action.payload,
      };
    case "SEND_MESSAGE":
      const { roomId, message } = action.payload;
      const roomMessages = state.messages[roomId] || [];
      const updatedMessages = {
        ...state.messages,
        [roomId]: [...roomMessages, message],
      };

      // Update last message in room
      const updatedRooms = state.rooms.map((room) =>
        room.id === roomId
          ? {
              ...room,
              lastMessage: message.content,
              timestamp: message.timestamp,
            }
          : room
      );

      return {
        ...state,
        messages: updatedMessages,
        rooms: updatedRooms,
      };
    case "SET_CHAT_ROOMS":
      return {
        ...state,
        rooms: action.payload,
      };
    case "SET_CHAT_MESSAGES":
      return {
        ...state,
        messages: {
          ...state.messages,
          [action.payload.roomId]: action.payload.messages,
        },
      };
    case "ADD_COURSE_GROUPS":
      // Add course groups to rooms if they don't already exist
      const existingRoomIds = new Set(state.rooms.map((room) => room.id));
      const newCourseGroups = action.payload.filter(
        (group) => !existingRoomIds.has(group.id)
      );

      // If no new groups, return current state
      if (newCourseGroups.length === 0) {
        return state;
      }

      // Create empty message arrays for new rooms
      const newMessages = { ...state.messages };
      newCourseGroups.forEach((group) => {
        if (!newMessages[group.id]) {
          newMessages[group.id] = [];
        }
      });

      return {
        ...state,
        rooms: [...state.rooms, ...newCourseGroups],
        messages: newMessages,
      };
    case "SET_COURSE_GROUPS_LOADED":
      return {
        ...state,
        courseGroupsLoaded: action.payload,
      };
    case "SEARCH_CHATS":
      const query = action.payload.toLowerCase();

      // If the query is a special filter
      if (query === "direct-filter") {
        const directChats = state.rooms.filter((room) => !room.isGroup);
        return {
          ...state,
          rooms: directChats,
        };
      } else if (query === "group-filter") {
        const groupChats = state.rooms.filter((room) => room.isGroup);
        return {
          ...state,
          rooms: groupChats,
        };
      }

      // Regular search
      if (!query) {
        return {
          ...state,
          rooms: state.rooms,
        };
      }

      const filteredRooms = state.rooms.filter(
        (room) =>
          room.name.toLowerCase().includes(query) ||
          room.lastMessage.toLowerCase().includes(query)
      );

      return { ...state, rooms: filteredRooms };
    case "SEARCH_MESSAGES":
      // Implement message search in future enhancement
      return state;
    case "SET_LOADING":
      return {
        ...state,
        isLoading: action.payload,
      };
    case "SET_ERROR":
      return {
        ...state,
        error: action.payload,
      };
    default:
      return state;
  }
};

// Create context
const ChatContext = createContext<ChatContextType | undefined>(undefined);

// Context provider
export const ChatProvider = ({ children }: { children: ReactNode }) => {
  const [state, dispatch] = useReducer(chatReducer, initialState);
  const [webSocketConnected, setWebSocketConnected] = useState(false);

  // Services
  const webSocketService = ChatWebSocketService.getInstance();
  const courseGroupService = CourseGroupService.getInstance();
  const chatApiService = ChatApiService.getInstance();

  // Load chat rooms function - defined at the component level so it can be called from anywhere
  const loadChatRooms = async () => {
    try {
      dispatch({ type: "SET_LOADING", payload: true });
      dispatch({ type: "SET_ERROR", payload: null });

      console.log("=== CHAT CONTEXT: Starting loadChatRooms ===");

      // Fetch all chat groups from API
      console.log("Fetching chat groups from API...");
      const chatGroups = await chatApiService.getAllChatGroups();
      console.log("Raw chat groups from API:", chatGroups);
      console.log("Chat groups count:", chatGroups?.length || 0);

      if (chatGroups && chatGroups.length > 0) {
        // The getAllChatGroups function already returns ChatRoom objects, no need to convert again
        console.log("Chat groups are already in ChatRoom format:", chatGroups);

        // Set the chat rooms in state
        dispatch({ type: "SET_CHAT_ROOMS", payload: chatGroups });

        // Set active room if there are rooms and no active room is set
        if (chatGroups.length > 0 && !state.activeRoomId) {
          console.log("Setting active room to:", chatGroups[0].id);
          dispatch({ type: "SET_ACTIVE_ROOM", payload: chatGroups[0].id });
        }
      } else {
        console.log("No chat groups found from API, setting empty array");
        dispatch({ type: "SET_CHAT_ROOMS", payload: [] });
      }

      // Try to load course groups as well if no chat groups were found or few groups
      const currentRoomsCount = chatGroups?.length || 0;
      if (currentRoomsCount < 2) {
        console.log("Loading course groups as fallback...");
        try {
          const courseGroups = await courseGroupService.fetchStudentCourseGroups();
          console.log("Course groups result:", courseGroups);
          if (courseGroups && courseGroups.length > 0) {
            console.log("Adding course groups to state");
            dispatch({ type: "ADD_COURSE_GROUPS", payload: courseGroups });
          }
        } catch (courseError) {
          console.error("Failed to load course groups:", courseError);
          // Don't show an error to the user for this since it's supplementary
        }
      }

      dispatch({ type: "SET_COURSE_GROUPS_LOADED", payload: true });
      console.log("=== CHAT CONTEXT: loadChatRooms completed successfully ===");
    } catch (error) {
      console.error("=== CHAT CONTEXT: loadChatRooms failed ===", error);
      dispatch({
        type: "SET_ERROR",
        payload: `Failed to load chat rooms: ${error}. Please check your connection and try again.`,
      });
      dispatch({ type: "SET_CHAT_ROOMS", payload: [] });
    } finally {
      dispatch({ type: "SET_LOADING", payload: false });
    }
  };

  // Load chat rooms on component mount
  useEffect(() => {
    loadChatRooms();
  }, []);



  // Connect to WebSocket when active room changes
  useEffect(() => {
    if (!state.activeRoomId) return;

    const activeRoom = state.rooms.find(
      (room) => room.id === state.activeRoomId
    );
    if (!activeRoom) return;

    // Disconnect previous connections
    webSocketService.disconnect();
    setWebSocketConnected(false);

    const connectWebSocket = async () => {
      try {
        dispatch({ type: "SET_LOADING", payload: true });
        console.log("Connecting to WebSocket for room:", activeRoom.id);

        // Connect to the appropriate WebSocket endpoint based on room type
        if (activeRoom.isGroup) {
          await webSocketService.connectToGroupChat(activeRoom.id);
        } else {
          // For direct messages, connect with the other user's ID
          const user = await getCurrentUser();
          if (!user) {
            console.error("Cannot connect to WebSocket: No user found");
            return;
          }

          const otherParticipant = activeRoom.participants.find(
            (p: any) => p.id !== user.id
          );
          if (otherParticipant) {
            await webSocketService.connectToDirectChat(otherParticipant.id);
          }
        }

        // Always connect to notifications
        await webSocketService.connectToNotifications();
        setWebSocketConnected(true);
      } catch (error) {
        console.error("Failed to connect to WebSocket:", error);
        dispatch({
          type: "SET_ERROR",
          payload: "Failed to connect to chat server",
        });
      } finally {
        dispatch({ type: "SET_LOADING", payload: false });
      }
    };

    connectWebSocket();

    // Set up message listener
    webSocketService.onMessage((data) => {
      if (!data) return;
      console.log("WebSocket message received:", data);

      // Use the formatWebSocketMessage helper to ensure consistent message format
      const message = formatWebSocketMessage(data);
      console.log("Formatted message:", message);

      if (state.activeRoomId) {
        dispatch({
          type: "SEND_MESSAGE",
          payload: {
            roomId: state.activeRoomId,
            message,
          },
        });
      }
    });

    // Register connection state change listener
    webSocketService.onConnectionStateChange((connected) => {
      setWebSocketConnected(connected);
    });

    // Cleanup function
    return () => {
      webSocketService.disconnect();
      setWebSocketConnected(false);
    };
  }, [state.activeRoomId, state.rooms, webSocketService, dispatch]);

  // Actions
  const setActiveRoom = (roomId: string) => {
    dispatch({ type: "SET_ACTIVE_ROOM", payload: roomId });
  };

  const sendMessage = async (content: string) => {
    if (!content.trim() || !state.activeRoomId) return;

    const activeRoom = state.rooms.find(
      (room) => room.id === state.activeRoomId
    );
    if (!activeRoom) return;

    // Get current user information
    const user = await getCurrentUser();
    if (!user) {
      dispatch({ type: "SET_ERROR", payload: "User not authenticated" });
      return;
    }

    // Create a new message
    const newMessage: ChatMessage = {
      id: Date.now().toString(),
      senderId: user.id,
      senderName: user.name,
      senderAvatar: user.avatar || "/logo.png",
      content: content.trim(),
      timestamp: new Date(),
    };

    try {
      // Send the message via WebSocket if connected
      if (webSocketConnected) {
        if (activeRoom.isGroup) {
          await webSocketService.sendGroupMessage(content.trim());
        } else {
          const otherParticipant = activeRoom.participants.find(
            (p: any) => p.id !== user.id
          );
          if (otherParticipant) {
            await webSocketService.sendDirectMessage(
              otherParticipant.id,
              content.trim()
            );
          }
        }
      }

      // Add message to local state (will be overwritten if we receive a socket response)
      dispatch({
        type: "SEND_MESSAGE",
        payload: { roomId: state.activeRoomId, message: newMessage },
      });
    } catch (error) {
      console.error("Failed to send message:", error);
      dispatch({ type: "SET_ERROR", payload: "Failed to send message" });
    }
  };

  const searchChats = (query: string) => {
    dispatch({ type: "SEARCH_CHATS", payload: query });
  };

  const searchMessages = (roomId: string, query: string) => {
    dispatch({ type: "SEARCH_MESSAGES", payload: { roomId, query } });
  };

  // Create a new chat group
  const createChatGroup = async (
    name: string,
    description: string,
    isPrivate: boolean,
    courseId?: string,
    initialMembers?: string[]
  ): Promise<boolean> => {
    try {
      dispatch({ type: "SET_LOADING", payload: true });

      // Use the chat API service to create a group with the correct format
      const groupData: ChatGroupCreateRequest = {
        name,
        description,
        type: "student", // Always use "student" as per API requirements
        course_id: courseId || "",
        is_private: isPrivate,
        initial_members: initialMembers || [],
      };

      await chatApiService.createChatGroup(groupData);

      // Reload chat rooms to include the new group
      await loadChatRooms();

      return true;
    } catch (error) {
      console.error("Failed to create chat group:", error);
      dispatch({
        type: "SET_ERROR",
        payload: "Failed to create chat group. Please try again later.",
      });
      return false;
    } finally {
      dispatch({ type: "SET_LOADING", payload: false });
    }
  };

  // Add a member to a chat group
  const addMemberToChatGroup = async (
    chatGroupId: string,
    memberId: string,
    isStudent: boolean,
    isAdmin: boolean
  ): Promise<boolean> => {
    try {
      dispatch({ type: "SET_LOADING", payload: true });

      // Use the chat API service to add a member
      await chatApiService.addMemberToChatGroup(chatGroupId, {
        student_id: isStudent ? memberId : undefined,
        teacher_id: !isStudent ? memberId : undefined,
        is_admin: isAdmin,
      });

      // Reload chat rooms to update the members
      await loadChatRooms();

      return true;
    } catch (error) {
      console.error("Failed to add member to chat group:", error);
      dispatch({
        type: "SET_ERROR",
        payload: "Failed to add member to chat group. Please try again later.",
      });
      return false;
    } finally {
      dispatch({ type: "SET_LOADING", payload: false });
    }
  };

  // Refresh chat rooms
  const refreshChatRooms = async (): Promise<void> => {
    await loadChatRooms();
  };

  // Join a specific chat group by ID
  const joinSpecificChatGroup = async (
    chatGroupId: string
  ): Promise<boolean> => {
    try {
      dispatch({ type: "SET_LOADING", payload: true });

      console.log(`Attempting to join specific chat group: ${chatGroupId}`);

      // Use the chat API service to join the specific chat group
      const result = await chatApiService.joinSpecificChatGroup(chatGroupId);

      if (result) {
        console.log(`Successfully joined chat group ${chatGroupId}`);
        // Refresh chat rooms to include the newly joined group
        await loadChatRooms();
        return true;
      } else {
        console.error(`Failed to join chat group ${chatGroupId}`);
        dispatch({
          type: "SET_ERROR",
          payload: "Failed to join chat group. Please try again later.",
        });
        return false;
      }
    } catch (error) {
      console.error(`Error joining specific chat group ${chatGroupId}:`, error);
      dispatch({
        type: "SET_ERROR",
        payload: "Failed to join chat group. Please try again later.",
      });
      return false;
    } finally {
      dispatch({ type: "SET_LOADING", payload: false });
    }
  };

  // Connect to a specific chat group via WebSocket
  const connectToSpecificChatGroup = async (chatGroupId: string): Promise<void> => {
    try {
      console.log(
        `Connecting to specific chat group via WebSocket: ${chatGroupId}`
      );

      // Use the WebSocket service to connect to the specific chat group
      await webSocketService.connectToSpecificChatGroup(chatGroupId);

      // Set this as the active room
      dispatch({ type: "SET_ACTIVE_ROOM", payload: chatGroupId });
    } catch (error) {
      console.error(
        `Error connecting to specific chat group ${chatGroupId}:`,
        error
      );
      dispatch({
        type: "SET_ERROR",
        payload: "Failed to connect to chat group. Please try again later.",
      });
    }
  };

  // Load chat group members
  const loadChatGroupMembers = async (chatGroupId: string): Promise<void> => {
    try {
      console.log(`Loading members for chat group: ${chatGroupId}`);
      const members = await chatApiService.getChatGroupMembers(chatGroupId);
      console.log(`Loaded ${members.length} members for group ${chatGroupId}:`, members);

      // Update the room with the loaded members
      const updatedRooms = state.rooms.map(room => {
        if (room.id === chatGroupId) {
          const participants = members.map(member => ({
            id: member.student_id || member.teacher_id || member.id || '',
            name: member.name || 'Unknown User',
            avatar: member.avatar || '/logo.png'
          }));
          return { ...room, participants };
        }
        return room;
      });

      dispatch({ type: "SET_CHAT_ROOMS", payload: updatedRooms });
    } catch (error) {
      console.error(`Failed to load members for chat group ${chatGroupId}:`, error);
      dispatch({
        type: "SET_ERROR",
        payload: "Failed to load chat group members.",
      });
    }
  };

  // Get chat group by ID
  const getChatGroupById = async (chatGroupId: string): Promise<any> => {
    try {
      console.log(`Fetching chat group details: ${chatGroupId}`);
      const chatGroup = await chatApiService.getChatGroupById(chatGroupId);
      console.log(`Fetched chat group details:`, chatGroup);
      return chatGroup;
    } catch (error) {
      console.error(`Failed to fetch chat group ${chatGroupId}:`, error);
      dispatch({
        type: "SET_ERROR",
        payload: "Failed to fetch chat group details.",
      });
      return null;
    }
  };

  const value: ChatContextType = {
    state,
    webSocketConnected,
    setActiveRoom,
    sendMessage,
    searchChats,
    searchMessages,
    createChatGroup,
    addMemberToChatGroup,
    refreshChatRooms,
    joinSpecificChatGroup,
    connectToSpecificChatGroup,
    loadChatGroupMembers,
    getChatGroupById,
  };

  return <ChatContext.Provider value={value}>{children}</ChatContext.Provider>;
};

// Custom hook for using the chat context
export const useChat = () => {
  const context = useContext(ChatContext);
  if (!context) {
    throw new Error("useChat must be used within a ChatProvider");
  }
  return context;
};
