import { ChatApiService, getCurrentUser } from './chat_api_service';
import { ChatWebSocketService } from './websocket_service';

/**
 * Utility functions for chat functionality
 * These functions provide easy-to-use methods for connecting to specific chat groups
 */

/**
 * Connect to a specific chat group by ID
 * This function handles both joining the group (if needed) and establishing WebSocket connection
 *
 * @param chatGroupId - The specific chat group ID (e.g., "2c8f3470-6f2c-4df4-97a3-c8c51430f696")
 * @param autoJoin - Whether to automatically join the group if not already a member (default: true)
 * @returns Promise<boolean> - True if successfully connected, false otherwise
 */
export async function connectToSpecificChatGroup(
  chatGroupId: string,
  autoJoin: boolean = true
): Promise<boolean> {
  try {
    // Verify user is authenticated
    const user = getCurrentUser();
    if (!user) {
      console.error('No authenticated user found - cannot connect to chat group');
      return false;
    }

    const userId = user.student_id || user.teacher_id || user.id;
    if (!userId) {
      console.error('No valid user ID found for authentication');
      return false;
    }

    console.log(`Connecting to specific chat group ${chatGroupId} as user ${userId}`);

    const chatApiService = ChatApiService.getInstance();
    const webSocketService = ChatWebSocketService.getInstance();

    // If autoJoin is enabled, try to join the group first
    if (autoJoin) {
      try {
        const joinResult = await chatApiService.joinSpecificChatGroup(chatGroupId);
        if (!joinResult) {
          console.warn(`Could not join chat group ${chatGroupId}, but will attempt to connect anyway`);
        }
      } catch (error) {
        console.warn(`Error joining chat group ${chatGroupId}:`, error);
        // Continue with connection attempt even if join fails
      }
    }

    // Connect via WebSocket
    webSocketService.connectToSpecificChatGroup(chatGroupId);

    console.log(`Successfully initiated connection to chat group ${chatGroupId}`);
    return true;

  } catch (error) {
    console.error(`Error connecting to specific chat group ${chatGroupId}:`, error);
    return false;
  }
}

/**
 * Get the WebSocket URL for a specific chat group
 *
 * @param chatGroupId - The chat group ID
 * @returns string | null - The WebSocket URL or null if user is not authenticated
 */
export function getWebSocketUrlForChatGroup(chatGroupId: string): string | null {
  const chatApiService = ChatApiService.getInstance();
  return chatApiService.getWebSocketUrl(chatGroupId);
}

/**
 * Check if a user can connect to a specific chat group
 * This function verifies authentication and optionally checks group membership
 *
 * @param chatGroupId - The chat group ID
 * @param checkMembership - Whether to check if user is a member of the group (default: false)
 * @returns Promise<boolean> - True if user can connect, false otherwise
 */
export async function canConnectToChatGroup(
  chatGroupId: string,
  checkMembership: boolean = false
): Promise<boolean> {
  try {
    // Check authentication
    const user = getCurrentUser();
    if (!user) {
      console.error('No authenticated user found');
      return false;
    }

    const userId = user.student_id || user.teacher_id || user.id;
    if (!userId) {
      console.error('No valid user ID found');
      return false;
    }

    // If membership check is not required, return true (user is authenticated)
    if (!checkMembership) {
      return true;
    }

    // Check if user has access to the chat group
    const chatApiService = ChatApiService.getInstance();
    try {
      const chatGroup = await chatApiService.getChatGroupById(chatGroupId);
      return chatGroup !== null;
    } catch (error) {
      console.error(`Error checking access to chat group ${chatGroupId}:`, error);
      return false;
    }

  } catch (error) {
    console.error(`Error checking if user can connect to chat group ${chatGroupId}:`, error);
    return false;
  }
}

/**
 * Validate a chat group ID format
 *
 * @param chatGroupId - The chat group ID to validate
 * @returns boolean - True if the format appears valid, false otherwise
 */
export function isValidChatGroupId(chatGroupId: string): boolean {
  if (!chatGroupId || typeof chatGroupId !== 'string') {
    return false;
  }

  // Check if it's a UUID format (basic validation)
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(chatGroupId);
}

/**
 * Get the current API and WebSocket base URLs
 *
 * @returns object with apiBaseUrl and wsBaseUrl
 */
export function getChatEndpoints() {
  return {
    apiBaseUrl: process.env.NEXT_PUBLIC_API_BASE_URL!,
    wsBaseUrl: process.env.NEXT_PUBLIC_WS_CHAT_BASE_URL!
  };
}


