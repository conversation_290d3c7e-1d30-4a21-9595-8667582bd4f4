// Chat participant interface
export interface ChatParticipant {
  id: string;
  name: string;
  avatar: string;
}

// Chat room/group interface
export interface ChatRoom {
  id: string;
  name: string;
  lastMessage: string;
  timestamp: Date;
  isGroup: boolean;
  avatar: string;
  participants: ChatParticipant[];
}

// Chat message interface
export interface ChatMessage {
  id: string;
  senderId: string;
  senderName: string;
  senderAvatar: string;
  content: string;
  timestamp: Date;
}

// Chat state interface for managing active room and messages
export interface ChatState {
  activeRoomId: string | null;
  rooms: ChatRoom[];
  messages: Record<string, ChatMessage[]>;
  isLoading: boolean;
  error: string | null;
  courseGroupsLoaded: boolean;
}

// WebSocket message interface to handle different message formats from server
export interface WebSocketMessage {
  id?: string;
  _id?: string; // Alternative ID field from some APIs
  content?: string;
  timestamp?: string;
  created_at?: string; // Alternative timestamp field
  sender?: {
    id?: string;
    name?: string;
    avatar?: string;
  };
  sender_id?: string; // Alternative sender field format
  sender_name?: string;
  sender_avatar?: string;
  type?: string; // Message type (text, system, etc.)
  attachments?: any[];
}

// Chat context interface for global state management
export interface ChatContextType {
  state: ChatState;
  webSocketConnected: boolean;
  setActiveRoom: (roomId: string) => void;
  sendMessage: (content: string) => void;
  searchChats: (query: string) => void;
  searchMessages: (roomId: string, query: string) => void;
  createChatGroup: (name: string, description: string, isPrivate: boolean, courseId?: string, initialMembers?: string[]) => Promise<boolean>;
  addMemberToChatGroup: (chatGroupId: string, memberId: string, isStudent: boolean, isAdmin: boolean) => Promise<boolean>;
  refreshChatRooms: () => Promise<void>;
}
