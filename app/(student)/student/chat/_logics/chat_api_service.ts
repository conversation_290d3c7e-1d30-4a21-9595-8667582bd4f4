import {
  ChatRoom,
  ChatGroupCreateRequest,
  ChatGroupAddMemberRequest,
  ChatGroupInvitationRequest,
  ChatGroupResponse,
  ChatGroupMember,
} from "./types";

// Get current user data from studentStore, userStore, or localStorage
export const getCurrentUser = async () => {
  if (typeof window !== "undefined") {
    try {
      // Try to get from zustand stores first
      try {
        // Import the stores dynamically to avoid Next.js SSR issues
        const { useStudentStore } = require("@/store/studentStore");
        const { useUserStore } = require("@/store/userStore");

        // Check studentStore first
        const studentState = useStudentStore.getState();
        if (studentState.student) {
          const student = studentState.student;
          return {
            id: student.id,
            student_id: student.id,
            name: `${student.first_name} ${student.last_name}`,
            avatar: student.profile_image || "/logo.png",
          };
        }

        // Check userStore as fallback
        const userState = useUserStore.getState();
        if (userState.user) {
          const user = userState.user;
          return {
            id: user.id,
            student_id: user.id,
            name: user.name || `${user.first_name} ${user.last_name}`,
            avatar: user.profile_image || "/logo.png",
          };
        }
      } catch (storeError) {
        console.error("Error accessing stores:", storeError);
      }

      // Fallback to localStorage if stores failed
      const userData = localStorage.getItem("user");
      if (userData) {
        const user = JSON.parse(userData);
        console.log("getCurrentUser - Found user in localStorage:", user.id);
        return {
          id: user.id,
          student_id: user.student_id || user.id,
          teacher_id: user.teacher_id,
          name: user.name || user.username || "User",
          avatar: user.avatar || user.profile_image || "/logo.png",
        };
      }

      // Try to decode user info from access token
      const Cookies = await import("js-cookie");
      const accessToken = Cookies.default.get("access_token");
      if (accessToken) {
        try {
          const base64Url = accessToken.split('.')[1];
          const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
          const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
            return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
          }).join(''));

          const payload = JSON.parse(jsonPayload);
          const userId = payload.sub || payload.user_id || payload.id;
          if (userId) {
            console.log("getCurrentUser - Found user in token:", userId);
            return {
              id: userId,
              student_id: userId,
              name: payload.name || "Current User",
              avatar: "/logo.png",
            };
          }
        } catch (tokenError) {
          console.warn("Error decoding access token:", tokenError);
        }
      }
    } catch (error) {
      console.error("Error getting user data:", error);
    }
  }

  console.warn("getCurrentUser - No user found in any source (stores, localStorage, token)");
  return null;
};

// Chat API Service - Uses useApi hook for all API endpoints
export class ChatApiService {
  private static instance: ChatApiService;
  private wsBaseUrl: string = process.env.NEXT_PUBLIC_WS_CHAT_BASE_URL!;

  constructor() {
    console.log("Using WebSocket base URL:", this.wsBaseUrl);
  }

  // Helper method to make API requests using axios directly (like other parts of the codebase)
  private async makeRequest(method: "GET" | "POST" | "PUT" | "DELETE", endpoint: string, data?: any, contentType: string = "application/json") {
    // Use dynamic imports to avoid SSR issues
    const axios = (await import('axios')).default;
    const Cookies = (await import('js-cookie')).default;

    const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;
    const token = Cookies.get("access_token");

    console.log(`ChatAPI - Making ${method} request to ${endpoint}`);
    console.log(`ChatAPI - Token exists: ${!!token}`);
    console.log(`ChatAPI - API Base URL: ${API_BASE_URL}`);

    try {
      const response = await axios({
        method,
        url: `${API_BASE_URL}${endpoint}`,
        data: data || null,
        headers: {
          'Content-Type': contentType,
          Authorization: token ? `Bearer ${token}` : "",
        },
      });

      console.log(`ChatAPI - ${method} ${endpoint} response status:`, response.status);
      return response;
    } catch (err: any) {
      console.error(`ChatAPI - ${method} ${endpoint} error:`, err);
      return err?.response;
    }
  }

  public static getInstance(): ChatApiService {
    if (!ChatApiService.instance) {
      ChatApiService.instance = new ChatApiService();
    }
    return ChatApiService.instance;
  }

  /**
   * Create a new chat group
   * POST /chat-group/
   */
  public async createChatGroup(
    groupData: ChatGroupCreateRequest
  ): Promise<ChatGroupResponse> {
    try {
      console.log("Creating chat group with data:", groupData);

      const response = await this.makeRequest(
        "POST",
        "/chat-group",
        groupData,
        "application/json"
      );

      console.log("Create chat group response:", response);

      if (response && response.status >= 200 && response.status < 300) {
        return response.data.data || response.data;
      } else {
        throw new Error(
          `Failed to create chat group: ${response?.status} - ${
            response?.data?.detail || "Unknown error"
          }`
        );
      }
    } catch (error) {
      console.error("Error creating chat group:", error);
      throw error;
    }
  }

  /**
   * Add a member to a chat group
   * POST /chat-group/{chat_group_id}/members
   */
  public async addMemberToChatGroup(
    chatGroupId: string,
    memberData: ChatGroupAddMemberRequest
  ): Promise<boolean> {
    try {
      console.log(`Adding member to chat group ${chatGroupId}:`, memberData);
      const response = await this.makeRequest(
        "POST",
        `/chat-group/${chatGroupId}/members`,
        memberData,
        "application/json"
      );

      console.log("Add member response:", response);

      if (response && response.status >= 200 && response.status < 300) {
        return true;
      } else {
        throw new Error(
          `Failed to add member to chat group: ${response?.status} - ${
            response?.data?.detail || "Unknown error"
          }`
        );
      }
    } catch (error) {
      console.error(`Error adding member to chat group ${chatGroupId}:`, error);
      throw error;
    }
  }

  /**
   * Create a chat group for a course (teacher only)
   * POST /chat-group/course/{course_id}
   */
  public async createCourseGroup(courseId: string): Promise<ChatGroupResponse> {
    try {
      console.log(`Creating course chat group for course ${courseId}`);
      const response = await this.makeRequest(
        "POST",
        `/chat-group/course/${courseId}`,
        null,
        "application/json"
      );

      console.log("Create course group response:", response);

      if (response && response.status >= 200 && response.status < 300) {
        return response.data.data;
      } else {
        throw new Error(
          `Failed to create course chat group: ${response?.status} - ${
            response?.data?.detail || "Unknown error"
          }`
        );
      }
    } catch (error) {
      console.error(
        `Error creating course chat group for course ${courseId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Create an invitation to join a chat group
   * POST /chat-group/{chat_group_id}/invitations
   */
  public async createChatGroupInvitation(
    chatGroupId: string,
    invitationData: ChatGroupInvitationRequest
  ): Promise<boolean> {
    try {
      console.log(
        `Creating invitation for chat group ${chatGroupId}:`,
        invitationData
      );
      const response = await this.makeRequest(
        "POST",
        `/chat-group/${chatGroupId}/invitations`,
        invitationData,
        "application/json"
      );

      console.log("Create invitation response:", response);

      if (response && response.status >= 200 && response.status < 300) {
        return true;
      } else {
        throw new Error(
          `Failed to create chat group invitation: ${response?.status} - ${
            response?.data?.detail || "Unknown error"
          }`
        );
      }
    } catch (error) {
      console.error(
        `Error creating invitation for chat group ${chatGroupId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Get a chat group by ID
   * GET /chat-group/{chat_group_id}
   */
  public async getChatGroupById(
    chatGroupId: string
  ): Promise<ChatGroupResponse> {
    try {
      console.log(`Fetching chat group ${chatGroupId}`);
      const response = await this.makeRequest(
        "GET",
        `/chat-group/${chatGroupId}`,
        null,
        "application/json"
      );

      console.log("Get chat group response:", response);

      if (response && response.status >= 200 && response.status < 300) {
        return response.data.data;
      } else {
        throw new Error(
          `Failed to fetch chat group: ${response?.status} - ${
            response?.data?.detail || "Unknown error"
          }`
        );
      }
    } catch (error) {
      console.error(`Error fetching chat group ${chatGroupId}:`, error);
      throw error;
    }
  }

  /**
   * Get all chat groups for the current user
   * GET /chat-group/
   */
  public async getAllChatGroups(): Promise<ChatGroupResponse[]> {
    try {
      // Get the current user for authentication
      const user = await getCurrentUser();
      if (!user) {
        console.error("No authenticated user found");
        return [];
      }

      console.log("Current user:", user);
      const userId = user.student_id || user.teacher_id || user.id;

      if (!userId) {
        console.error("No user ID found for authentication");
        return [];
      }

      const response = await this.makeRequest(
        "GET",
        "/chat-group",
        null,
        "application/json"
      );

      console.log("Chat groups response:", response);

      if (response && response.status >= 200 && response.status < 300) {
        // Handle different possible response structures
        let chatGroupsArray = [];

        if (response.data) {
          // Try different possible response structures
          if (response.data.data && Array.isArray(response.data.data)) {
            chatGroupsArray = response.data.data;
            console.log("Found chat groups in response.data.data:", chatGroupsArray);
          } else if (response.data.chat_groups && Array.isArray(response.data.chat_groups)) {
            chatGroupsArray = response.data.chat_groups;
            console.log("Found chat groups in response.data.chat_groups:", chatGroupsArray);
          } else if (Array.isArray(response.data)) {
            chatGroupsArray = response.data;
            console.log("Found chat groups in response.data:", chatGroupsArray);
          } else {
            console.warn("Unexpected response structure:", response.data);
            console.log("Response data keys:", Object.keys(response.data || {}));
            return [];
          }
        }

        console.log("Final chat groups array:", chatGroupsArray);

        if (chatGroupsArray.length === 0) {
          console.log("No chat groups found for user - this might be normal for new users");
          return [];
        }

        // Convert the raw API response to our ChatRoom format
        return chatGroupsArray.map((group: any) => {
          console.log("Processing group:", group);

          // Safely handle group data with fallbacks
          const groupId = group.id || group.chat_group_id || `temp-${Date.now()}`;
          const groupName = group.name || group.group_name || "Unnamed Group";
          const lastMessage = group.last_message?.content || group.lastMessage || "No messages yet";
          const timestamp = group.updated_at || group.created_at || new Date().toISOString();

          const chatRoom = {
            id: groupId,
            name: groupName,
            lastMessage: lastMessage,
            timestamp: new Date(timestamp),
            isGroup: true,
            avatar: "/logo.png",
            participants: (group.members || []).map((member: any) => ({
              id: member.student_id || member.teacher_id || member.user_id || member.id || `member-${Date.now()}`,
              name: member.name || member.username || member.first_name || "Unknown User",
              avatar: member.avatar || member.profile_image || "/logo.png",
            })),
          };

          console.log("Converted to chat room:", chatRoom);
          return chatRoom;
        });
      } else {
        console.error("Failed to fetch chat groups:", response?.status, response?.data);
        return [];
      }
    } catch (error) {
      console.error("Error fetching chat groups:", error);
      return []; // Return empty array instead of throwing to prevent UI crashes
    }
  }

  /**
   * Get all members of a chat group
   * GET /chat-group/{chat_group_id}/members
   */
  public async getChatGroupMembers(
    chatGroupId: string
  ): Promise<ChatGroupMember[]> {
    try {
      console.log(`Fetching members for chat group ${chatGroupId}`);
      const response = await this.makeRequest(
        "GET",
        `/chat-group/${chatGroupId}/members`,
        null,
        "application/json"
      );

      console.log("Get chat group members response:", response);

      if (response && response.status >= 200 && response.status < 300) {
        return response.data.data || [];
      } else {
        throw new Error(
          `Failed to fetch chat group members: ${response?.status} - ${
            response?.data?.detail || "Unknown error"
          }`
        );
      }
    } catch (error) {
      console.error(
        `Error fetching members for chat group ${chatGroupId}:`,
        error
      );
      throw error;
    }
  }



  /**
   * Join a chat group (for students joining course groups)
   * POST /chat-group/{chat_group_id}/join
   */
  public async joinChatGroup(chatGroupId: string): Promise<boolean> {
    try {
      console.log(`Joining chat group ${chatGroupId}`);
      const response = await this.makeRequest(
        "POST",
        `/chat-group/${chatGroupId}/join`,
        null,
        "application/json"
      );

      console.log("Join chat group response:", response);

      if (response && response.status >= 200 && response.status < 300) {
        return true;
      } else {
        throw new Error(
          `Failed to join chat group: ${response?.status} - ${
            response?.data?.detail || "Unknown error"
          }`
        );
      }
    } catch (error) {
      console.error(`Error joining chat group ${chatGroupId}:`, error);
      return false;
    }
  }

  /**
   * Join a specific chat group by ID (for the WebSocket connection pattern)
   * This method ensures the user is authenticated and can connect to the specified chat group
   * @param chatGroupId - The specific chat group ID like "2c8f3470-6f2c-4df4-97a3-c8c51430f696"
   */
  public async joinSpecificChatGroup(chatGroupId: string): Promise<boolean> {
    try {
      // First verify the user is authenticated
      const user = await getCurrentUser();
      if (!user) {
        console.error("No authenticated user found - cannot join chat group");
        return false;
      }

      const userId = user.student_id || user.teacher_id || user.id;
      if (!userId) {
        console.error("No valid user ID found for authentication");
        return false;
      }

      console.log(
        `Attempting to join specific chat group ${chatGroupId} as user ${userId}`
      );

      // Check if the chat group exists and user has access
      const chatGroup = await this.getChatGroupById(chatGroupId);
      if (!chatGroup) {
        console.error(
          `Chat group ${chatGroupId} not found or user doesn't have access`
        );
        return false;
      }

      // Join the chat group
      const joinResult = await this.joinChatGroup(chatGroupId);
      if (joinResult) {
        console.log(`Successfully joined chat group ${chatGroupId}`);
        return true;
      } else {
        console.error(`Failed to join chat group ${chatGroupId}`);
        return false;
      }
    } catch (error) {
      console.error(`Error joining specific chat group ${chatGroupId}:`, error);
      return false;
    }
  }

  /**
   * Get WebSocket URL for connecting to a specific chat group
   * @param chatGroupId - The chat group ID
   * @returns WebSocket URL or null if user is not authenticated
   */
  public async getWebSocketUrl(chatGroupId: string): Promise<string | null> {
    const user = await getCurrentUser();
    if (!user) {
      console.error(
        "No authenticated user found - cannot generate WebSocket URL"
      );
      return null;
    }

    const userId = user.student_id || user.teacher_id || user.id;
    if (!userId) {
      console.error("No valid user ID found for WebSocket authentication");
      return null;
    }

    // Use the environment variable for WebSocket base URL
    const wsUrl = `${this.wsBaseUrl}/${chatGroupId}?user_id=${userId}`;
    console.log(`Generated WebSocket URL: ${wsUrl}`);
    return wsUrl;
  }

  /**
   * Convert ChatGroupResponse to ChatRoom format
   */
  public chatGroupResponseToChatRoom(group: ChatGroupResponse): ChatRoom {
    console.log("Converting chat group to chat room:", group);

    // Create participants list from members (handle case where members might be undefined)
    const participants = Array.isArray(group.members)
      ? group.members.map((member) => ({
          id: member.student_id || member.teacher_id || "",
          name: member.name || "Unknown",
          avatar: member.avatar || "/logo.png",
        }))
      : [];

    // Make sure we have a valid timestamp
    let timestamp;
    try {
      timestamp = new Date(group.updated_at || group.created_at || Date.now());
    } catch (e) {
      timestamp = new Date();
    }

    // Create a valid ChatRoom object
    const chatRoom: ChatRoom = {
      id: group.id || `temp-${Date.now()}`,
      name: group.name || "Unnamed Group",
      lastMessage: group.last_message?.content || "No messages yet",
      timestamp: timestamp,
      isGroup: true,
      avatar: group.course_id
        ? `/assets/courses/${group.course_id}.png`
        : "/logo.png",
      participants: participants,
    };

    console.log("Converted to chat room:", chatRoom);
    return chatRoom;
  }
}
