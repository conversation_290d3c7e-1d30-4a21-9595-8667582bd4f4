import {
  ChatRoom,
  ChatGroupCreateRequest,
  ChatGroupAddMemberRequest,
  ChatGroupInvitationRequest,
  ChatGroupResponse,
  ChatGroupMember
} from './types';

// Get current user data from studentStore, userStore, or localStorage
export const getCurrentUser = () => {
  if (typeof window !== 'undefined') {
    try {
      // Try to get from zustand stores first
      try {
        // Import the stores dynamically to avoid Next.js SSR issues
        const { useStudentStore } = require('@/store/studentStore');
        const { useUserStore } = require('@/store/userStore');

        // Check studentStore first
        const studentState = useStudentStore.getState();
        if (studentState.student) {
          const student = studentState.student;
          return {
            id: student.id,
            student_id: student.id,
            name: `${student.first_name} ${student.last_name}`,
            avatar: student.profile_image || '/logo.png'
          };
        }

        // Check userStore as fallback
        const userState = useUserStore.getState();
        if (userState.user) {
          const user = userState.user;
          return {
            id: user.id,
            student_id: user.id,
            name: user.name || `${user.first_name} ${user.last_name}`,
            avatar: user.profile_image || '/logo.png'
          };
        }
      } catch (storeError) {
        console.error('Error accessing stores:', storeError);
      }

      // Fallback to localStorage if stores failed
      const userData = localStorage.getItem('user');
      if (userData) {
        const user = JSON.parse(userData);
        return {
          id: user.id,
          student_id: user.student_id || user.id,
          teacher_id: user.teacher_id,
          name: user.name || user.username || 'User',
          avatar: user.avatar || user.profile_image || '/logo.png'
        };
      }
    } catch (error) {
      console.error('Error getting user data:', error);
    }
  }
  return null;
};

// Chat  Service
export class ChatApiService {
  private static instance: ChatApiService;
  private apiBaseUrl: string = process.env.NEXT_PUBLIC_API_BASE_URL!;
  private wsBaseUrl: string = process.env.NEXT_PUBLIC_WS_CHAT_BASE_URL!;
  private request: any;

  constructor() {
    console.log('Using API base URL:', this.apiBaseUrl);
    console.log('Using WebSocket base URL:', this.wsBaseUrl);

    // Initialize request function using the same pattern as the rest of the codebase
    this.initializeRequest();
  }

  private initializeRequest() {
    // Use the same request pattern as the rest of the codebase
    this.request = async (method: string, endpoint: string, data: any = null, contentType = 'application/json') => {
      try {
        const { useApi } = await import('@/hooks/useRequest');
        const { request } = useApi();

        // Convert full URL to endpoint if needed
        const cleanEndpoint = endpoint.startsWith('http')
          ? endpoint.replace(this.apiBaseUrl, '')
          : endpoint;

        return await request(method as any, cleanEndpoint, data, contentType);
      } catch (error) {
        console.error('Error with API request:', error);
        // Fallback to direct fetch with proper authentication
        return this.fallbackRequest(method, endpoint, data, contentType);
      }
    };
  }

  private async fallbackRequest(method: string, url: string, data: any = null, contentType = 'application/json') {
    let token = '';
    if (typeof window !== 'undefined') {
      try {
        const Cookies = await import('js-cookie');
        token = Cookies.default.get('access_token') || '';
      } catch (err) {
        console.error('Error getting token from cookies:', err);
      }
    }

    const options: RequestInit = {
      method,
      headers: {
        'Content-Type': contentType,
        'Authorization': token ? `Bearer ${token}` : ''
      }
    };

    if (data) {
      options.body = typeof data === 'string' ? data : JSON.stringify(data);
    }

    console.log(`Making ${method} request to ${url} with auth:`, token ? 'Bearer token present' : 'No bearer token');
    const response = await fetch(`${this.apiBaseUrl}${url}`, options);
    const responseData = await response.json();

    return {
      status: response.status,
      data: responseData
    };
  }



  public static getInstance(): ChatApiService {
    if (!ChatApiService.instance) {
      ChatApiService.instance = new ChatApiService();
    }
    return ChatApiService.instance;
  }

  /**
   * Create a new chat group
   * POST /chat-group/
   */
  public async createChatGroup(groupData: ChatGroupCreateRequest): Promise<ChatGroupResponse> {
    try {
      console.log('Creating chat group with data:', groupData);

      const endpoint = `${this.apiBaseUrl}/chat-group`;
      console.log(`Using endpoint: ${endpoint}`);

      // Request with user
      const response = await this.request(
        'POST',
        endpoint,
        groupData,
        'application/json'
      );

      console.log('Create chat group response:', response);

      if (response && response.status >= 200 && response.status < 300) {
        // Follow the pattern from questionBank.tsx for parsing responses
        return response.data.data || response.data;
      } else {
        console.warn('Chat group creation response:', response);

        return {
          id: `temp-${Date.now()}`,
          name: groupData.name,
          description: groupData.description,
          type: groupData.type,
          course_id: groupData.course_id,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          is_private: groupData.is_private,
          members: []
        };
      }
    } catch (error) {
      console.error('Error creating chat group:', error);
      throw error;
    }
  }

  /**
   * Add a member to a chat group
   * POST /chat-group/{chat_group_id}/members
   */
  public async addMemberToChatGroup(chatGroupId: string, memberData: ChatGroupAddMemberRequest): Promise<boolean> {
    try {
      console.log(`Adding member to chat group ${chatGroupId}:`, memberData);
      const response = await this.request(
        'POST',
        `/chat-group/${chatGroupId}/members`,
        memberData,
        'application/json'
      );

      console.log('Add member response:', response);

      if (response && response.status >= 200 && response.status < 300) {
        return true;
      } else {
        throw new Error(`Failed to add member to chat group: ${response?.status} - ${response?.data?.detail || 'Unknown error'}`);
      }
    } catch (error) {
      console.error(`Error adding member to chat group ${chatGroupId}:`, error);
      throw error;
    }
  }

  /**
   * Create a chat group for a course (teacher only)
   * POST /chat-group/course/{course_id}
   */
  public async createCourseGroup(courseId: string): Promise<ChatGroupResponse> {
    try {
      console.log(`Creating course chat group for course ${courseId}`);
      const response = await this.request(
        'POST',
        `/chat-group/course/${courseId}`,
        null,
        'application/json'
      );

      console.log('Create course group response:', response);

      if (response && response.status >= 200 && response.status < 300) {
        return response.data.data;
      } else {
        throw new Error(`Failed to create course chat group: ${response?.status} - ${response?.data?.detail || 'Unknown error'}`);
      }
    } catch (error) {
      console.error(`Error creating course chat group for course ${courseId}:`, error);
      throw error;
    }
  }

  /**
   * Create an invitation to join a chat group
   * POST /chat-group/{chat_group_id}/invitations
   */
  public async createChatGroupInvitation(chatGroupId: string, invitationData: ChatGroupInvitationRequest): Promise<boolean> {
    try {
      console.log(`Creating invitation for chat group ${chatGroupId}:`, invitationData);
      const response = await this.request(
        'POST',
        `/chat-group/${chatGroupId}/invitations`,
        invitationData,
        'application/json'
      );

      console.log('Create invitation response:', response);

      if (response && response.status >= 200 && response.status < 300) {
        return true;
      } else {
        throw new Error(`Failed to create chat group invitation: ${response?.status} - ${response?.data?.detail || 'Unknown error'}`);
      }
    } catch (error) {
      console.error(`Error creating invitation for chat group ${chatGroupId}:`, error);
      throw error;
    }
  }

  /**
   * Get a chat group by ID
   * GET /chat-group/{chat_group_id}
   */
  public async getChatGroupById(chatGroupId: string): Promise<ChatGroupResponse> {
    try {
      console.log(`Fetching chat group ${chatGroupId}`);
      const response = await this.request(
        'GET',
        `/chat-group/${chatGroupId}`,
        null,
        'application/json'
      );

      console.log('Get chat group response:', response);

      if (response && response.status >= 200 && response.status < 300) {
        return response.data.data;
      } else {
        throw new Error(`Failed to fetch chat group: ${response?.status} - ${response?.data?.detail || 'Unknown error'}`);
      }
    } catch (error) {
      console.error(`Error fetching chat group ${chatGroupId}:`, error);
      throw error;
    }
  }

  /**
   * Get all chat groups for the current user
   * GET /chat-group/
   */
  public async getAllChatGroups(): Promise<ChatGroupResponse[]> {
    try {
      // Get the current user for authentication
      const user = getCurrentUser();
      if (!user) {
        console.error('No authenticated user found');
        return [];
      }

      // Use the production API endpoint
      const endpoint = `${this.apiBaseUrl}/chat-group`;
      console.log(`Fetching chat groups from: ${endpoint}`);

      console.log('Current user:', user);
      const userId = user.student_id || user.teacher_id || user.id;

      if (!userId) {
        console.error('No user ID found for authentication');
        return [];
      }

      // Add the user_id as a query parameter to ensure the backend knows which user's groups to fetch
      const response = await this.request(
        'GET',
        endpoint,
        null,
        'application/json'
      );

      console.log('Chat groups response:', response);

      if (response && response.status >= 200 && response.status < 300) {
        // The API returns { data: { chat_groups: [...] } }
        const chatGroups = response.data?.data?.chat_groups;
        console.log('Extracted chat_groups:', chatGroups);

        if (Array.isArray(chatGroups)) {
          return chatGroups;
        } else {
          console.warn('Chat groups is not an array or is undefined:', chatGroups);
          return [];
        }
      } else {
        throw new Error(`Failed to fetch chat groups: ${response?.status} - ${response?.data?.detail || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Error fetching chat groups:', error);
      return []; // Return empty array instead of throwing to prevent UI crashes
    }
  }

  /**
   * Get all members of a chat group
   * GET /chat-group/{chat_group_id}/members
   */
  public async getChatGroupMembers(chatGroupId: string): Promise<ChatGroupMember[]> {
    try {
      console.log(`Fetching members for chat group ${chatGroupId}`);
      const response = await this.request(
        'GET',
        `/chat-group/${chatGroupId}/members`,
        null,
        'application/json'
      );

      console.log('Get chat group members response:', response);

      if (response && response.status >= 200 && response.status < 300) {
        return response.data.data || [];
      } else {
        throw new Error(`Failed to fetch chat group members: ${response?.status} - ${response?.data?.detail || 'Unknown error'}`);
      }
    } catch (error) {
      console.error(`Error fetching members for chat group ${chatGroupId}:`, error);
      throw error;
    }
  }

  /**
   * Get the chat history for a specific chat group or direct chat
   * GET /chat/history/{chat_id}
   */
  public async getChatHistory(chatId: string | null): Promise<any[]> {
    if (!chatId) {
      console.error('Cannot fetch chat history: No chat ID provided');
      return [];
    }

    try {
      console.log(`Fetching chat history for chat ${chatId}`);
      try {
        const response = await this.request(
          'GET',
          `${this.apiBaseUrl}/chat/history/${chatId}`,
          null,
          'application/json'
        );

        console.log('Get chat history response:', response);

        if (response && response.status >= 200 && response.status < 300) {
          const data = response.data.data || [];
          if (!Array.isArray(data)) {
            console.warn('API returned non-array data for chat history:', data);
            return [];
          }
          return data;
        }
      } catch (firstError) {
        console.warn('Failed to fetch chat history from API:', firstError);
        // Continue to fallback
      }

      // Fallback to mock data
      console.log('Using mock chat history data');
      return [
        {
          id: '1',
          content: 'Welcome to the chat room!',
          timestamp: new Date(Date.now() - 3600000).toISOString(),
          sender: {
            id: 'system',
            name: 'System',
            avatar: '/logo.png'
          }
        },
        {
          id: '2',
          content: 'Hey everyone, how are you doing?',
          timestamp: new Date(Date.now() - 1800000).toISOString(),
          sender: {
            id: '2',
            name: 'Instructor',
            avatar: '/logo.png'
          }
        },
        {
          id: '3',
          content: 'I have a question about the latest assignment.',
          timestamp: new Date(Date.now() - 900000).toISOString(),
          sender: {
            id: '1',
            name: 'You',
            avatar: '/logo.png'
          }
        }
      ];
    } catch (error) {
      console.error(`Error fetching chat history for chat ${chatId}:`, error);
      return []; // Return empty array to avoid UI crashes
    }
  }

  /**
   * Join a chat group (for students joining course groups)
   * POST /chat-group/{chat_group_id}/join
   */
  public async joinChatGroup(chatGroupId: string): Promise<boolean> {
    try {
      console.log(`Joining chat group ${chatGroupId}`);
      const response = await this.request(
        'POST',
        `/chat-group/${chatGroupId}/join`,
        null,
        'application/json'
      );

      console.log('Join chat group response:', response);

      if (response && response.status >= 200 && response.status < 300) {
        return true;
      } else {
        throw new Error(`Failed to join chat group: ${response?.status} - ${response?.data?.detail || 'Unknown error'}`);
      }
    } catch (error) {
      console.error(`Error joining chat group ${chatGroupId}:`, error);
      return false;
    }
  }

  /**
   * Join a specific chat group by ID (for the WebSocket connection pattern)
   * This method ensures the user is authenticated and can connect to the specified chat group
   * @param chatGroupId - The specific chat group ID like "2c8f3470-6f2c-4df4-97a3-c8c51430f696"
   */
  public async joinSpecificChatGroup(chatGroupId: string): Promise<boolean> {
    try {
      // First verify the user is authenticated
      const user = getCurrentUser();
      if (!user) {
        console.error('No authenticated user found - cannot join chat group');
        return false;
      }

      const userId = user.student_id || user.teacher_id || user.id;
      if (!userId) {
        console.error('No valid user ID found for authentication');
        return false;
      }

      console.log(`Attempting to join specific chat group ${chatGroupId} as user ${userId}`);

      // Check if the chat group exists and user has access
      const chatGroup = await this.getChatGroupById(chatGroupId);
      if (!chatGroup) {
        console.error(`Chat group ${chatGroupId} not found or user doesn't have access`);
        return false;
      }

      // Join the chat group
      const joinResult = await this.joinChatGroup(chatGroupId);
      if (joinResult) {
        console.log(`Successfully joined chat group ${chatGroupId}`);
        return true;
      } else {
        console.error(`Failed to join chat group ${chatGroupId}`);
        return false;
      }
    } catch (error) {
      console.error(`Error joining specific chat group ${chatGroupId}:`, error);
      return false;
    }
  }

  /**
   * Get WebSocket URL for connecting to a specific chat group
   * @param chatGroupId - The chat group ID
   * @returns WebSocket URL or null if user is not authenticated
   */
  public getWebSocketUrl(chatGroupId: string): string | null {
    const user = getCurrentUser();
    if (!user) {
      console.error('No authenticated user found - cannot generate WebSocket URL');
      return null;
    }

    const userId = user.student_id || user.teacher_id || user.id;
    if (!userId) {
      console.error('No valid user ID found for WebSocket authentication');
      return null;
    }

    // Use the environment variable for WebSocket base URL
    const wsUrl = `${this.wsBaseUrl}/${chatGroupId}?user_id=${userId}`;
    console.log(`Generated WebSocket URL: ${wsUrl}`);
    return wsUrl;
  }

  /**
   * Convert ChatGroupResponse to ChatRoom format
   */
  public chatGroupResponseToChatRoom(group: ChatGroupResponse): ChatRoom {
    console.log('Converting chat group to chat room:', group);

    // Create participants list from members (handle case where members might be undefined)
    const participants = Array.isArray(group.members)
      ? group.members.map(member => ({
          id: member.student_id || member.teacher_id || '',
          name: member.name || 'Unknown',
          avatar: member.avatar || '/logo.png'
        }))
      : [];

    // Make sure we have a valid timestamp
    let timestamp;
    try {
      timestamp = new Date(group.updated_at || group.created_at || Date.now());
    } catch (e) {
      timestamp = new Date();
    }

    // Create a valid ChatRoom object
    const chatRoom: ChatRoom = {
      id: group.id || `temp-${Date.now()}`,
      name: group.name || 'Unnamed Group',
      lastMessage: group.last_message?.content || "No messages yet",
      timestamp: timestamp,
      isGroup: true,
      avatar: group.course_id ? `/assets/courses/${group.course_id}.png` : '/logo.png',
      participants: participants
    };

    console.log('Converted to chat room:', chatRoom);
    return chatRoom;
  }
}
