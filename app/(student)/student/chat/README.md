# Chat System Documentation

## Overview

The chat system provides comprehensive functionality for real-time messaging with support for both group chats and direct messages. It uses a consistent endpoint pattern and requires user authentication before allowing chat access.

## Environment Configuration

The system uses environment variables for endpoint configuration:

```env
NEXT_PUBLIC_API_BASE_URL=https://naval-dorris-bridgesoft-b41775a4.koyeb.app/api/v1
NEXT_PUBLIC_WS_CHAT_BASE_URL=wss://naval-dorris-bridgesoft-b41775a4.koyeb.app/api/v1/ws/chat
```

## Authentication Requirements

Before users can access any chat functionality, they must:
1. <PERSON><PERSON> as either a student or teacher
2. Have a valid authentication token stored in cookies
3. Have a valid user ID (student_id, teacher_id, or id)

## API Endpoints

All chat-related API calls use the `useApi` hook from `@/hooks/useRequest` for consistency with the rest of the codebase:

### Chat Groups
- `GET /chat-group/` - Get all chat groups for the current user
- `POST /chat-group/` - Create a new chat group
- `GET /chat-group/{id}` - Get a specific chat group by ID
- `POST /chat-group/{id}/join` - Join a specific chat group
- `POST /chat-group/{id}/members` - Add a member to a chat group
- `GET /chat-group/{id}/members` - Get all members of a chat group



## WebSocket Connections

### Real-time Communication

All real-time messaging uses WebSocket connections with the `NEXT_PUBLIC_WS_CHAT_BASE_URL` environment variable:

1. **Group Chat**: `{NEXT_PUBLIC_WS_CHAT_BASE_URL}/{room_id}?user_id={user_id}`
2. **Direct Chat**: `{NEXT_PUBLIC_WS_CHAT_BASE_URL}/direct/{recipient_id}?user_id={user_id}`
3. **Notifications**: `{API_BASE_URL}/ws/notifications/{user_id}`

### Architecture Separation

- **API Endpoints**: Use `useApi` hook for all CRUD operations (create groups, join groups, manage members)
- **Real-time Messaging**: Use WebSocket connections for live chat communication

## File Structure

```
app/(student)/student/chat/
├── _logics/
│   ├── chat_api_service.ts      # API service with consistent endpoint usage
│   ├── chat_context.tsx         # React context for chat state management
│   ├── websocket_service.ts     # WebSocket service for real-time communication
│   ├── chat_utils.ts           # Utility functions for common chat operations
│   └── types.ts                # TypeScript interfaces and types
├── widgets/
│   ├── ChatContainer.tsx       # Main chat container
│   ├── ChatSidebar.tsx         # Chat sidebar with room list
│   ├── ChatMessageArea.tsx     # Message display area
│   └── ChatMessageInput.tsx    # Message input component
├── page.tsx                    # Main chat page
└── README.md                   # This documentation file
```

## Key Features

1. **Consistent API Usage**: All endpoints use the same `useApi` hook pattern as the rest of the codebase
2. **Authentication Integration**: Seamless integration with existing authentication system
3. **Environment Configuration**: Configurable endpoints via environment variables
4. **Error Handling**: Comprehensive error handling and user feedback
5. **Real-time Communication**: WebSocket-based real-time messaging
6. **Type Safety**: Full TypeScript support with proper interfaces
7. **Utility Functions**: Helper functions for common operations

## Security Considerations

- All API calls require valid authentication tokens
- WebSocket connections include user authentication
- Chat group access is validated server-side
- User permissions are checked before allowing operations

## Troubleshooting

### Common Issues

1. **Authentication Errors**: Ensure user is logged in and has valid tokens
2. **WebSocket Connection Failures**: Check network connectivity and authentication
3. **Chat Group Access Denied**: Verify user has permission to access the specific group
4. **Environment Variables**: Ensure all required environment variables are set

### Debug Information

The system provides extensive console logging for debugging:
- API request/response details
- WebSocket connection status
- Authentication verification
- Error messages with context
