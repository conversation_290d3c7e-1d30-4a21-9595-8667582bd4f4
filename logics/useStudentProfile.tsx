import { useState } from "react";
import { useApi } from "@/hooks/useRequest";
import { useNotification } from "@/hooks/useNotifs";
import { Form, Spin, message } from "antd";
import { useStudentStore } from "@/store/studentStore";
import { useUserStore } from "@/store/userStore";
import { processImageSource } from "@/utils/imageUtils";


export const useStudentProfile = () => {
  const { request } = useApi();
  const { showNotification, destroyNotifications } = useNotification();
  const [loading, setLoading] = useState(false);
  const [tokenExpired, setTokenExpired] = useState(false);
  const [form] = Form.useForm();

  const { student, setStudent } = useStudentStore();
  const { user } = useUserStore();

  // Fetch Student Profile
  const fetchStudentProfile = async () => {
    try {
      console.log('StudentProfile - Starting fetch');
      setLoading(true);
      const response = await request("GET", "/protected/me", null, "");
      console.log('StudentProfile - /me response:', response);

      if (response && response.status === 200) {
        console.log('StudentProfile - Response data:', response.data);
        // For students, the API returns 'student'
        const studentData = response.data.data.student;
        console.log('StudentProfile - Student data:', studentData);

        if (studentData) {
          // Process profile and cover images with proper format for display
          if (response.data.data.profile_image) {
            studentData.profile_image = response.data.data.profile_image;
            studentData.profile_image_path = processImageSource(response.data.data.profile_image);
          } else if (studentData.profile_image) {
            studentData.profile_image_path = processImageSource(studentData.profile_image);
          } else if (studentData.profile_image_path) {
            studentData.profile_image_path = processImageSource(studentData.profile_image_path);
          } else if (studentData.profile_image_path_server) {
            studentData.profile_image_path = processImageSource(studentData.profile_image_path_server);
          }

          if (response.data.data.cover_image) {
            studentData.cover_image = response.data.data.cover_image;
            studentData.cover_image_path = processImageSource(response.data.data.cover_image);
          } else if (studentData.cover_image) {
            studentData.cover_image_path = processImageSource(studentData.cover_image);
          } else if (studentData.cover_image_path) {
            studentData.cover_image_path = processImageSource(studentData.cover_image_path);
          } else if (studentData.cover_image_path_server) {
            studentData.cover_image_path = processImageSource(studentData.cover_image_path_server);
          }

          setStudent(studentData);
          console.log('StudentProfile - Student data set in store:', studentData);
          return studentData;
        } else {
          console.log('StudentProfile - No student data found in response');
          message.error('No student data found');
          return null;
        }
      } else if (response?.status === 401 || response?.status === 403) {
        console.log('StudentProfile - Token expired or invalid');
        setTokenExpired(true);
        return null;
      } else {
        console.log('StudentProfile - Error response:', response);
        message.error('Failed to fetch profile data');
        return null;
      }
    } catch (error: any) {
      console.error('StudentProfile - Error fetching profile:', error);
      if (error?.response?.status === 401 || error?.response?.status === 403) {
        setTokenExpired(true);
      } else {
        message.error('Failed to fetch profile data');
      }
      return null;
    } finally {
      setLoading(false);
    }
  };

  // Update Student Profile
  const updateStudentProfile = async (values: any) => {
    try {
      setLoading(true);
      if (!student?.id) {
        showNotification('error', 'Error', 'Student ID not found');
        return null;
      }

      const response = await request(
        "PUT",
        `/student/${student.id}`,
        values,
        "application/json"
      );

      if (response && response.status === 200) {
        const updatedStudent = {
          ...student,
          ...response.data.data,
        };

        // Process images if they exist
        if (updatedStudent.profile_image_path) {
          updatedStudent.profile_image_path = processImageSource(updatedStudent.profile_image_path);
        }

        if (updatedStudent.cover_image_path) {
          updatedStudent.cover_image_path = processImageSource(updatedStudent.cover_image_path);
        }

        setStudent(updatedStudent);
        showNotification('success', 'Success', 'Profile updated successfully');
        return updatedStudent;
      } else if (response?.status === 401 || response?.status === 403) {
        setTokenExpired(true);
        return null;
      } else {
        showNotification('error', 'Error', response?.data?.detail || 'Failed to update profile');
        return null;
      }
    } catch (error: any) {
      if (error?.response?.status === 401 || error?.response?.status === 403) {
        setTokenExpired(true);
      } else {
        showNotification('error', 'Error', error?.response?.data?.detail || 'An error occurred while updating your profile');
      }
      return null;
    } finally {
      setLoading(false);
    }
  };

  // Upload Student Profile Image
  const uploadProfileImage = async (file: File) => {
    try {
      if (!student?.id) {
        showNotification('error', 'Error', 'Student ID not found');
        return null;
      }

      // Show notification while uploading
      showNotification('info', 'Uploading Image', 'Uploading your profile image...', true, <Spin />);

      // Create FormData object
      const formData = new FormData();
      formData.append('file', file);

      // Make the API request
      const response = await request(
        "POST",
        `/student/${student.id}/upload-profile-image`,
        formData,
        "multipart/form-data"
      );

      destroyNotifications();

      if (response && response.status === 200) {
        // Update the student state with the new image
        const updatedStudent = { ...student };

        // Use the uploaded file for immediate display
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = (e) => {
          if (e.target && e.target.result) {
            updatedStudent.profile_image_path = e.target.result as string;

            // Update the student state with the server response data
            if (response.data.data.profile_image) {
              updatedStudent.profile_image = response.data.data.profile_image;
            } else if (response.data.data.profile_image_path) {
              updatedStudent.profile_image_path_server = response.data.data.profile_image_path;
            }

            setStudent({...updatedStudent});
          }
        };

        setStudent(updatedStudent);
        showNotification('success', 'Success', 'Profile image updated successfully');
        return updatedStudent;
      } else if (response?.status === 401 || response?.status === 403) {
        setTokenExpired(true);
        return null;
      } else {
        showNotification('error', 'Error', response?.data?.detail || 'Failed to upload profile image');
        return null;
      }
    } catch (error: any) {
      if (error?.response?.status === 401 || error?.response?.status === 403) {
        setTokenExpired(true);
      } else {
        destroyNotifications();
        showNotification('error', 'Error', error?.response?.data?.detail || 'An error occurred while uploading your profile image');
      }
      return null;
    }
  };

  // Upload Student Cover Image
  const uploadCoverImage = async (file: File) => {
    try {
      if (!student?.id) {
        showNotification('error', 'Error', 'Student ID not found');
        return null;
      }

      // Show notification while uploading
      showNotification('info', 'Uploading Image', 'Uploading your cover image...', true, <Spin />);

      // Create FormData object
      const formData = new FormData();
      formData.append('file', file);

      // Make the API request
      const response = await request(
        "POST",
        `/student/${student.id}/upload-cover-image`,
        formData,
        "multipart/form-data"
      );

      destroyNotifications();

      if (response && response.status === 200) {
        // Update the student state with the new image
        const updatedStudent = { ...student };

        // Use the uploaded file for immediate display
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = (e) => {
          if (e.target && e.target.result) {
            updatedStudent.cover_image_path = e.target.result as string;

            // Update the student state with the server response data
            if (response.data.data.cover_image) {
              updatedStudent.cover_image = response.data.data.cover_image;
            } else if (response.data.data.cover_image_path) {
              updatedStudent.cover_image_path_server = response.data.data.cover_image_path;
            }

            setStudent({...updatedStudent});
          }
        };

        setStudent(updatedStudent);
        showNotification('success', 'Success', 'Cover image updated successfully');
        return updatedStudent;
      } else if (response?.status === 401 || response?.status === 403) {
        setTokenExpired(true);
        return null;
      } else {
        showNotification('error', 'Error', response?.data?.detail || 'Failed to upload cover image');
        return null;
      }
    } catch (error: any) {
      if (error?.response?.status === 401 || error?.response?.status === 403) {
        setTokenExpired(true);
      } else {
        destroyNotifications();
        showNotification('error', 'Error', error?.response?.data?.detail || 'An error occurred while uploading your cover image');
      }
      return null;
    }
  };

  // Get formatted profile image URL
  const getProfileImageUrl = () => {
    if (!student) return '';

    if (student.profile_image_path) {
      return student.profile_image_path;
    }

    if (student.profile_image) {
      return processImageSource(student.profile_image);
    }

    if (student.profile_image_path_server) {
      return processImageSource(student.profile_image_path_server);
    }

    return '';
  };

  // Get formatted cover image URL
  const getCoverImageUrl = () => {
    if (!student) {
      console.log('StudentProfile - getCoverImageUrl: No student data available');
      return '';
    }

    console.log('StudentProfile - getCoverImageUrl: Checking image sources', {
      cover_image_path: student.cover_image_path || 'not available',
      cover_image: student.cover_image || 'not available',
      cover_image_path_server: student.cover_image_path_server || 'not available'
    });

    try {
      // If we have a data URL from a recent upload (client-side), use it directly
      if (student.cover_image_path && student.cover_image_path.startsWith('data:')) {
        console.log('StudentProfile - getCoverImageUrl: Using data URL from cover_image_path');
        return student.cover_image_path;
      }

      // Try processing the server image URL first as it's most likely to be current
      if (student.cover_image) {
        console.log('StudentProfile - getCoverImageUrl: Processing cover_image');
        const processed = processImageSource(student.cover_image);
        console.log('StudentProfile - getCoverImageUrl: Processed result', processed);
        if (processed) return processed;
      }

      // Try the server path next
      if (student.cover_image_path_server) {
        console.log('StudentProfile - getCoverImageUrl: Processing cover_image_path_server');
        const processed = processImageSource(student.cover_image_path_server);
        console.log('StudentProfile - getCoverImageUrl: Processed result', processed);
        if (processed) return processed;
      }

      // Fallback to cover_image_path if it's not a data URL (which we handled first)
      if (student.cover_image_path && !student.cover_image_path.startsWith('data:')) {
        console.log('StudentProfile - getCoverImageUrl: Using cover_image_path as fallback');
        const processed = processImageSource(student.cover_image_path);
        console.log('StudentProfile - getCoverImageUrl: Processed result', processed);
        if (processed) return processed;
      }
    } catch (error) {
      console.error('StudentProfile - getCoverImageUrl: Error processing image', error);
    }

    console.log('StudentProfile - getCoverImageUrl: No cover image found or all processing failed');
    return; // Return the default cover image placeholder
  };

  return {
    fetchStudentProfile,
    updateStudentProfile,
    uploadProfileImage,
    uploadCoverImage,
    getProfileImageUrl,
    getCoverImageUrl,
    loading,
    tokenExpired,
    form
  };
};
