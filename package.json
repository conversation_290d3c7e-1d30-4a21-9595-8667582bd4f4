{"name": "learn-konnect", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "prepare": "husky install"}, "dependencies": {"@ant-design/icons": "^5.6.1", "@emailjs/browser": "^4.4.1", "@emoji-mart/data": "^1.2.1", "@emoji-mart/react": "^1.1.1", "@tailwindcss/postcss": "^4.0.0", "antd": "^5.23.3", "axios": "^1.7.9", "date-fns": "^4.1.0", "emoji-mart": "^5.6.0", "emoji-picker-react": "^4.12.2", "framer-motion": "^12.4.7", "js-cookie": "^3.0.5", "next": "15.1.6", "rc-tween-one": "^3.0.6", "react": "^18.2.0", "react-dom": "^18.2.0", "react-intersection-observer": "^9.15.1", "react-responsive": "^10.0.1", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@eslint/js": "^9.21.0", "@types/date-fns": "^2.5.3", "@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9.21.0", "eslint-config-next": "15.1.6", "eslint-plugin-react": "^7.37.4", "globals": "^16.0.0", "husky": "^8.0.0", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "typescript": "5.8.3", "typescript-eslint": "^8.26.0"}}